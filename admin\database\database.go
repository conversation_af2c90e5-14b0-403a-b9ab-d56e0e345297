package database

import (
	"ai_select_admin/config"
	"ai_select_admin/models"
	"database/sql"
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var DB *gorm.DB

func Init() error {
	var err error

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		config.AppConfig.Database.Username,
		config.AppConfig.Database.Password,
		config.AppConfig.Database.Host,
		config.AppConfig.Database.Port,
		config.AppConfig.Database.Database,
		config.AppConfig.Database.Charset,
	)

	// 连接数据库
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   config.AppConfig.Database.Prefix,
			SingularTable: true,
		},
	})

	if err != nil {
		return nil
	}
	var sqlDB *sql.DB
	if sqlDB, err = DB.DB(); err != nil {
		return err
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	// 创建默认管理员账户
	createDefaultAdmin()
	return nil
}

func createDefaultAdmin() {
	// 自动迁移数据库表
	DB.AutoMigrate(
		&models.Admin{},
		&models.Category{},
		&models.Video{},
		&models.QuestionCategory{},
		&models.Question{},
		&models.Exam{},
		&models.ExamQuestion{},
		&models.AssessmentCategory{},
		&models.AssessmentVideo{},
		&models.AssessmentVideoRelation{},
	)

	var count int64
	DB.Model(&models.Admin{}).Count(&count)
	if count == 0 {
		// 创建默认管理员
		salt := models.GenerateSalt()
		password := models.HashPassword("yanqu0501", salt)

		admin := models.Admin{
			Username: "admin",
			Password: password,
			Salt:     salt,
			Email:    "<EMAIL>",
			Status:   1,
		}

		if err := DB.Create(&admin).Error; err != nil {
			log.Printf("创建默认管理员失败: %v", err)
		} else {
			log.Println("默认管理员创建成功 - 用户名: admin, 密码: yanqu0501")
		}
	}
}
