package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// QuestionType 题目类型
type QuestionType int

const (
	QuestionTypeChoice QuestionType = 1 // 选择题
)

// QuestionDifficulty 题目难度
type QuestionDifficulty int

const (
	DifficultyEasy   QuestionDifficulty = 1 // 简单
	DifficultyMedium QuestionDifficulty = 2 // 中等
	DifficultyHard   QuestionDifficulty = 3 // 困难
)

// QuestionOption 题目选项
type QuestionOption struct {
	Key   string `json:"key"`   // 选项标识 A、B、C、D
	Value string `json:"value"` // 选项内容
}

// Question 题目模型
type Question struct {
	ID             uint                   `json:"id" gorm:"primaryKey;autoIncrement"`
	Title          string                 `json:"title" gorm:"type:varchar(200);not null;comment:题目标题"`
	Content        string                 `json:"content" gorm:"type:text;not null;comment:题目内容"`
	Type           QuestionType           `json:"type" gorm:"type:tinyint(1);default:1;comment:题目类型 1选择题"`
	Options        string                 `json:"-" gorm:"type:json;comment:选择项JSON"`
	OptionsData    []QuestionOption       `json:"options" gorm:"-"`
	CorrectAnswer  string                 `json:"correct_answer" gorm:"type:varchar(10);not null;comment:正确答案"`
	Difficulty     QuestionDifficulty     `json:"difficulty" gorm:"type:tinyint(1);default:1;comment:难度等级 1简单 2中等 3困难"`
	CategoryID     uint                   `json:"category_id" gorm:"type:int;default:0;comment:题库分类ID"`
	Status         int                    `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	DeletedAt      gorm.DeletedAt         `json:"-" gorm:"index"`

	// 关联
	Category *QuestionCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// BeforeSave GORM钩子：保存前处理
func (q *Question) BeforeSave(tx *gorm.DB) error {
	if len(q.OptionsData) > 0 {
		optionsJSON, err := json.Marshal(q.OptionsData)
		if err != nil {
			return err
		}
		q.Options = string(optionsJSON)
	}
	return nil
}

// AfterFind GORM钩子：查询后处理
func (q *Question) AfterFind(tx *gorm.DB) error {
	if q.Options != "" {
		return json.Unmarshal([]byte(q.Options), &q.OptionsData)
	}
	return nil
}

// QuestionListRequest 题目列表请求参数
type QuestionListRequest struct {
	Page       int                `form:"page" binding:"omitempty,min=1"`
	PageSize   int                `form:"page_size" binding:"omitempty,min=1,max=100"`
	Title      string             `form:"title"`
	Type       *QuestionType      `form:"type" binding:"omitempty,oneof=1"`
	Difficulty *QuestionDifficulty `form:"difficulty" binding:"omitempty,oneof=1 2 3"`
	CategoryID *uint              `form:"category_id"`
	Status     *int               `form:"status" binding:"omitempty,oneof=0 1"`
}

// QuestionCreateRequest 创建题目请求参数
type QuestionCreateRequest struct {
	Title         string             `json:"title" binding:"required,max=200"`
	Content       string             `json:"content" binding:"required,max=2000"`
	Type          QuestionType       `json:"type" binding:"required,oneof=1"`
	Options       []QuestionOption   `json:"options" binding:"required,min=2,max=6"`
	CorrectAnswer string             `json:"correct_answer" binding:"required,max=10"`
	Difficulty    QuestionDifficulty `json:"difficulty" binding:"required,oneof=1 2 3"`
	CategoryID    uint               `json:"category_id" binding:"omitempty,min=0"`
	Status        int                `json:"status" binding:"omitempty,oneof=0 1"`
}

// QuestionUpdateRequest 更新题目请求参数
type QuestionUpdateRequest struct {
	Title         string             `json:"title" binding:"required,max=200"`
	Content       string             `json:"content" binding:"required,max=2000"`
	Type          QuestionType       `json:"type" binding:"required,oneof=1"`
	Options       []QuestionOption   `json:"options" binding:"required,min=2,max=6"`
	CorrectAnswer string             `json:"correct_answer" binding:"required,max=10"`
	Difficulty    QuestionDifficulty `json:"difficulty" binding:"required,oneof=1 2 3"`
	CategoryID    uint               `json:"category_id" binding:"omitempty,min=0"`
	Status        int                `json:"status" binding:"omitempty,oneof=0 1"`
}

// QuestionResponse 题目响应数据
type QuestionResponse struct {
	ID             uint                        `json:"id"`
	Title          string                      `json:"title"`
	Content        string                      `json:"content"`
	Type           QuestionType                `json:"type"`
	TypeText       string                      `json:"type_text"`
	Options        []QuestionOption            `json:"options"`
	CorrectAnswer  string                      `json:"correct_answer"`
	Difficulty     QuestionDifficulty          `json:"difficulty"`
	DifficultyText string                      `json:"difficulty_text"`
	CategoryID     uint                        `json:"category_id"`
	Status         int                         `json:"status"`
	Category       *QuestionCategoryResponse   `json:"category,omitempty"`
	CreatedAt      time.Time                   `json:"created_at"`
	UpdatedAt      time.Time                   `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (q *Question) ToResponse() *QuestionResponse {
	typeText := ""
	switch q.Type {
	case QuestionTypeChoice:
		typeText = "选择题"
	}

	difficultyText := ""
	switch q.Difficulty {
	case DifficultyEasy:
		difficultyText = "简单"
	case DifficultyMedium:
		difficultyText = "中等"
	case DifficultyHard:
		difficultyText = "困难"
	}

	resp := &QuestionResponse{
		ID:             q.ID,
		Title:          q.Title,
		Content:        q.Content,
		Type:           q.Type,
		TypeText:       typeText,
		Options:        q.OptionsData,
		CorrectAnswer:  q.CorrectAnswer,
		Difficulty:     q.Difficulty,
		DifficultyText: difficultyText,
		CategoryID:     q.CategoryID,
		Status:         q.Status,
		CreatedAt:      q.CreatedAt,
		UpdatedAt:      q.UpdatedAt,
	}

	if q.Category != nil {
		resp.Category = q.Category.ToResponse()
	}

	return resp
}
