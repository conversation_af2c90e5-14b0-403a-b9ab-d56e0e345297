<template>
  <div class="exam-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="试卷标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入试卷标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增试卷
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column
          prop="title"
          label="试卷标题"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="total_score"
          label="总分"
          width="80"
          align="center"
        />
        <el-table-column
          prop="pass_score"
          label="及格分"
          width="80"
          align="center"
        />
        <el-table-column
          prop="time_limit"
          label="时长(分钟)"
          width="100"
          align="center"
        />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="info"
              size="small"
              @click="handleManageQuestions(row)"
            >
              题目管理
            </el-button>
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingId ? '编辑试卷' : '新增试卷'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="试卷标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入试卷标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="试卷描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入试卷描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="总分" prop="total_score">
          <el-input-number
            v-model="formData.total_score"
            :min="1"
            :max="1000"
            placeholder="试卷总分"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="及格分" prop="pass_score">
          <el-input-number
            v-model="formData.pass_score"
            :min="1"
            :max="formData.total_score || 100"
            placeholder="及格分数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="考试时长" prop="time_limit">
          <el-input-number
            v-model="formData.time_limit"
            :min="1"
            :max="600"
            placeholder="考试时长（分钟）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 题目管理对话框 -->
    <el-dialog
      v-model="questionDialogVisible"
      title="题目管理"
      width="1200px"
      @close="handleQuestionDialogClose"
    >
      <div class="question-management">
        <!-- 左侧：题目选择 -->
        <div class="question-selection">
          <h4>题目库</h4>
          <el-form
            :model="questionSearchForm"
            inline
            style="margin-bottom: 20px"
          >
            <el-form-item label="题目标题">
              <el-input
                v-model="questionSearchForm.title"
                placeholder="请输入题目标题"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="分类">
              <el-select
                v-model="questionSearchForm.category_id"
                placeholder="请选择分类"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="option in categorySelectOptions"
                  :key="option.id"
                  :label="option.label"
                  :value="option.id"
                  :disabled="option.disabled"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuestionSearch"
                >搜索</el-button
              >
            </el-form-item>
          </el-form>

          <el-table
            v-loading="questionLoading"
            :data="availableQuestions"
            style="width: 100%"
            max-height="400px"
            @selection-change="handleQuestionSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="title"
              label="题目标题"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column prop="category_name" label="分类" width="100" />
            <el-table-column prop="type" label="类型" width="80">
              <template #default="{ row }">
                {{ getQuestionTypeLabel(row.type) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="score"
              label="默认分值"
              width="80"
              align="center"
            />
          </el-table>
        </div>

        <!-- 右侧：已选题目 -->
        <div class="selected-questions">
          <h4>已选题目 ({{ selectedQuestions.length }})</h4>
          <el-table
            :data="selectedQuestions"
            style="width: 100%"
            max-height="400px"
          >
            <el-table-column
              prop="title"
              label="题目标题"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column prop="category_name" label="分类" width="100" />
            <el-table-column label="分值" width="100">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.exam_score"
                  :min="1"
                  :max="100"
                  size="small"
                  @change="updateQuestionScore($index, row.exam_score)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeQuestion($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div style="margin-top: 20px; text-align: center">
            <el-button
              type="primary"
              @click="saveExamQuestions"
              :loading="questionSubmitting"
            >
              保存题目设置
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import {
  getExamList,
  createExam,
  updateExam,
  deleteExam,
  getExam,
  updateExamQuestions,
} from "@/api/exam";
import { getQuestionList } from "@/api/question";
import { getQuestionCategoryTree } from "@/api/questionCategory";
import type { Exam } from "@/types/exam";
import type { Question } from "@/types/question";
import type { QuestionSelectOption } from "@/types/exam";
import {
  flattenCategoryTreeWithPath,
  type CategoryOption,
} from "@/utils/category";

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const questionLoading = ref(false);
const questionSubmitting = ref(false);
const dialogVisible = ref(false);
const questionDialogVisible = ref(false);
const editingId = ref<number | null>(null);
const managingExamId = ref<number | null>(null);
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive({
  title: "",
  status: undefined as number | undefined,
});

// 题目搜索表单
const questionSearchForm = reactive({
  title: "",
  category_id: undefined as number | undefined,
});

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 表格数据
const tableData = ref<Exam[]>([]);
const categorySelectOptions = ref<CategoryOption[]>([]);
const availableQuestions = ref<QuestionSelectOption[]>([]);
const selectedQuestions = ref<QuestionSelectOption[]>([]);

// 表单数据
const formData = reactive({
  title: "",
  description: "",
  total_score: 100,
  pass_score: 60,
  time_limit: 60,
  status: 1,
});

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: "请输入试卷标题", trigger: "blur" },
    {
      min: 1,
      max: 200,
      message: "标题长度在 1 到 200 个字符",
      trigger: "blur",
    },
  ],
  description: [
    { max: 500, message: "描述长度不能超过 500 个字符", trigger: "blur" },
  ],
  total_score: [
    { required: true, message: "请设置总分", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 1000,
      message: "总分在 1 到 1000 之间",
      trigger: "blur",
    },
  ],
  pass_score: [
    { required: true, message: "请设置及格分", trigger: "blur" },
    { type: "number", min: 1, message: "及格分必须大于 0", trigger: "blur" },
  ],
  time_limit: [
    { required: true, message: "请设置考试时长", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 600,
      message: "考试时长在 1 到 600 分钟之间",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 获取题目类型标签
const getQuestionTypeLabel = (type: number) => {
  const types = { 1: "单选题", 2: "多选题", 3: "判断题" };
  return types[type as keyof typeof types] || "未知";
};

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    const response = await getQuestionCategoryTree();
    const categories = response.data || [];
    categorySelectOptions.value = flattenCategoryTreeWithPath(categories);
  } catch (error) {
    console.error("获取分类树失败:", error);
  }
};

// 获取试卷列表
const fetchExamList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm,
    };
    const response = await getExamList(params);
    tableData.value = response.data?.list || [];
    pagination.total = response.data?.total || 0;
  } catch (error) {
    console.error("获取试卷列表失败:", error);
    ElMessage.error("获取试卷列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取题目列表
const fetchQuestionList = async () => {
  try {
    questionLoading.value = true;
    const params = {
      page: 1,
      page_size: 100,
      status: 1,
      ...questionSearchForm,
    };
    const response = await getQuestionList(params);
    const questions = response.data?.list || [];

    // 转换为选择选项格式
    availableQuestions.value = questions.map((q: Question) => ({
      id: q.id,
      title: q.title,
      content: q.content,
      type: q.type,
      difficulty: q.difficulty,
      score: q.score,
      category_name: q.category?.name || "",
      selected: false,
      exam_score: q.score,
    }));
  } catch (error) {
    console.error("获取题目列表失败:", error);
    ElMessage.error("获取题目列表失败");
  } finally {
    questionLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchExamList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: "",
    status: undefined,
  });
  pagination.current = 1;
  fetchExamList();
};

// 题目搜索
const handleQuestionSearch = () => {
  fetchQuestionList();
};

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size;
  pagination.current = 1;
  fetchExamList();
};

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current;
  fetchExamList();
};

// 新增试卷
const handleAdd = () => {
  editingId.value = null;
  resetForm();
  dialogVisible.value = true;
};

// 编辑试卷
const handleEdit = (row: Exam) => {
  editingId.value = row.id;
  Object.assign(formData, {
    title: row.title,
    description: row.description,
    total_score: row.total_score,
    pass_score: row.pass_score,
    time_limit: row.time_limit,
    status: row.status,
  });
  dialogVisible.value = true;
};

// 删除试卷
const handleDelete = async (row: Exam) => {
  try {
    await ElMessageBox.confirm(`确定要删除试卷"${row.title}"吗？`, "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteExam(row.id);
    ElMessage.success("删除成功");
    fetchExamList();
  } catch (error: any) {
    if (error !== "cancel") {
      console.error("删除试卷失败:", error);
      ElMessage.error(error.response?.data?.message || "删除失败");
    }
  }
};

// 管理题目
const handleManageQuestions = async (row: Exam) => {
  managingExamId.value = row.id;

  // 获取试卷详情和已选题目
  try {
    const response = await getExam(row.id);
    const exam = response.data;

    // 设置已选题目
    selectedQuestions.value = (exam.questions || []).map((eq: any) => ({
      id: eq.question.id,
      title: eq.question.title,
      content: eq.question.content,
      type: eq.question.type,
      difficulty: eq.question.difficulty,
      score: eq.question.score,
      category_name: eq.question.category?.name || "",
      selected: true,
      exam_score: eq.score,
    }));

    // 获取可选题目
    await fetchQuestionList();
    questionDialogVisible.value = true;
  } catch (error) {
    console.error("获取试卷详情失败:", error);
    ElMessage.error("获取试卷详情失败");
  }
};

// 题目选择改变
const handleQuestionSelectionChange = (selection: QuestionSelectOption[]) => {
  // 添加新选择的题目到已选列表
  selection.forEach((question) => {
    if (!selectedQuestions.value.find((q) => q.id === question.id)) {
      selectedQuestions.value.push({
        ...question,
        selected: true,
        exam_score: question.score,
      });
    }
  });
};

// 更新题目分值
const updateQuestionScore = (index: number, score: number) => {
  if (selectedQuestions.value[index]) {
    selectedQuestions.value[index].exam_score = score;
  }
};

// 移除题目
const removeQuestion = (index: number) => {
  selectedQuestions.value.splice(index, 1);
};

// 保存试卷题目设置
const saveExamQuestions = async () => {
  if (!managingExamId.value) return;

  try {
    questionSubmitting.value = true;

    const questions = selectedQuestions.value.map((q, index) => ({
      question_id: q.id,
      score: q.exam_score || q.score,
      sort: index + 1,
    }));

    await updateExamQuestions(managingExamId.value, { questions });
    ElMessage.success("题目设置保存成功");
    questionDialogVisible.value = false;
  } catch (error: any) {
    console.error("保存题目设置失败:", error);
    ElMessage.error(error.response?.data?.message || "保存失败");
  } finally {
    questionSubmitting.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    if (editingId.value) {
      await updateExam(editingId.value, formData);
      ElMessage.success("更新成功");
    } else {
      await createExam(formData);
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    fetchExamList();
  } catch (error: any) {
    console.error("提交失败:", error);
    ElMessage.error(error.response?.data?.message || "操作失败");
  } finally {
    submitting.value = false;
  }
};

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields();
  resetForm();
};

// 题目管理对话框关闭
const handleQuestionDialogClose = () => {
  managingExamId.value = null;
  selectedQuestions.value = [];
  availableQuestions.value = [];
  Object.assign(questionSearchForm, {
    title: "",
    category_id: undefined,
  });
};

// 重置表单
const resetForm = () => {
  editingId.value = null;
  Object.assign(formData, {
    title: "",
    description: "",
    total_score: 100,
    pass_score: 60,
    time_limit: 60,
    status: 1,
  });
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return "";
  return new Date(dateTime).toLocaleString("zh-CN");
};

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryTree();
  fetchExamList();
});
</script>

<style scoped>
.exam-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.question-management {
  display: flex;
  gap: 20px;
  height: 500px;
}

.question-selection {
  flex: 1;
  border-right: 1px solid #ebeef5;
  padding-right: 20px;
}

.selected-questions {
  flex: 1;
  padding-left: 20px;
}

.question-selection h4,
.selected-questions h4 {
  margin: 0 0 20px 0;
  color: #303133;
}
</style>
