import request from '@/utils/request'
import type { 
  AssessmentVideo, 
  AssessmentVideoListRequest, 
  AssessmentVideoCreateRequest, 
  AssessmentVideoUpdateRequest 
} from '@/types/assessmentVideo'

// 获取考核视频列表
export function getAssessmentVideoList(params: AssessmentVideoListRequest) {
  return request({
    url: '/admin/assessment-videos',
    method: 'get',
    params
  })
}

// 获取考核视频详情
export function getAssessmentVideo(id: number) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'get'
  })
}

// 创建考核视频
export function createAssessmentVideo(data: AssessmentVideoCreateRequest) {
  return request({
    url: '/admin/assessment-videos',
    method: 'post',
    data
  })
}

// 更新考核视频
export function updateAssessmentVideo(id: number, data: AssessmentVideoUpdateRequest) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'put',
    data
  })
}

// 删除考核视频
export function deleteAssessmentVideo(id: number) {
  return request({
    url: `/admin/assessment-videos/${id}`,
    method: 'delete'
  })
}
