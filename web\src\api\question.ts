import request from '@/utils/request'
import type { 
  Question, 
  QuestionListRequest, 
  QuestionCreateRequest, 
  QuestionUpdateRequest 
} from '@/types/question'

// 获取题目列表
export function getQuestionList(params: QuestionListRequest) {
  return request({
    url: '/admin/questions',
    method: 'get',
    params
  })
}

// 获取题目详情
export function getQuestion(id: number) {
  return request({
    url: `/admin/questions/${id}`,
    method: 'get'
  })
}

// 创建题目
export function createQuestion(data: QuestionCreateRequest) {
  return request({
    url: '/admin/questions',
    method: 'post',
    data
  })
}

// 更新题目
export function updateQuestion(id: number, data: QuestionUpdateRequest) {
  return request({
    url: `/admin/questions/${id}`,
    method: 'put',
    data
  })
}

// 删除题目
export function deleteQuestion(id: number) {
  return request({
    url: `/admin/questions/${id}`,
    method: 'delete'
  })
}
