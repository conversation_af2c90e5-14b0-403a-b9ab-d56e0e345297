<template>
  <div class="video-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="视频标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入视频标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="上传中" :value="1" />
            <el-option label="转码中" :value="2" />
            <el-option label="正常" :value="3" />
            <el-option label="失败" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchVideoList" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button
          type="primary"
          @click="fetchVideoList"
          :loading="loading"
          circle
          class="refresh-btn"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
        <el-button type="success" @click="showUploadDialog = true">
          <el-icon><Upload /></el-icon>
          上传视频
        </el-button>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="table-section">
      <el-table :data="videoList" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="封面" width="120">
          <template #default="{ row }">
            <div class="video-cover">
              <img
                v-if="row.cover_url"
                :src="row.cover_url"
                :alt="row.title"
                @click="handlePreview(row)"
              />
              <div v-else class="no-cover" @click="handlePreview(row)">
                <el-icon><VideoPlay /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="title"
          label="标题"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="file_name"
          label="文件名"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="时长" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>
        <el-table-column label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column label="分类" width="120">
          <template #default="{ row }">
            {{ row.category?.name || "未分类" }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handlePreview(row)"
              :disabled="row.status !== 3"
            >
              预览
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchVideoList"
          @current-change="fetchVideoList"
        />
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上传视频"
      width="700px"
      :close-on-click-modal="false"
    >
      <div class="upload-section">
        <!-- 文件选择 -->
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="false"
          accept="video/*"
          @change="handleFileSelect"
        >
          <el-button type="primary" class="upload-btn">
            <el-icon><Plus /></el-icon>
            选择视频文件
          </el-button>
        </el-upload>

        <!-- 上传列表 -->
        <div v-if="uploadList.length > 0" class="upload-list">
          <div
            v-for="(item, index) in uploadList"
            :key="index"
            class="upload-item"
          >
            <!-- 视频信息表单 -->
            <el-form :model="item" label-width="80px" class="video-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="视频标题" required>
                    <el-input
                      v-model="item.title"
                      placeholder="请输入视频标题"
                      maxlength="100"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属分类" required>
                    <el-select
                      v-model="item.category_id"
                      placeholder="请选择分类"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="category in categoryOptions"
                        :key="category.id"
                        :label="category.name"
                        :value="category.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="封面图片">
                <div class="cover-upload">
                  <el-upload
                    :auto-upload="false"
                    :show-file-list="false"
                    accept="image/*"
                    @change="(file) => handleCoverSelect(file, index)"
                  >
                    <div class="cover-preview">
                      <img
                        v-if="item.cover_url"
                        :src="item.cover_url"
                        alt="封面预览"
                        class="cover-image"
                      />
                      <div v-else class="cover-placeholder">
                        <el-icon><Plus /></el-icon>
                        <span>上传封面</span>
                      </div>
                    </div>
                  </el-upload>
                  <div class="cover-tips">
                    建议尺寸：16:9，支持 JPG、PNG 格式
                  </div>
                </div>
              </el-form-item>
            </el-form>

            <!-- 文件信息 -->
            <div class="file-info">
              <span class="file-name">{{ item.file.name }}</span>
              <span class="file-size">{{
                formatFileSize(item.file.size)
              }}</span>
            </div>

            <!-- 上传进度 -->
            <div class="upload-progress">
              <el-progress
                :percentage="item.progress.percent"
                :status="getProgressStatus(item.status)"
              />
            </div>

            <!-- 操作按钮 -->
            <div class="upload-actions">
              <el-button
                v-if="item.status === 'waiting'"
                type="primary"
                size="small"
                @click="startUpload(index)"
                :disabled="!item.title || !item.category_id"
              >
                开始上传
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="removeUploadItem(index)"
              >
                移除
              </el-button>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div v-if="uploadList.length > 0" class="batch-actions">
          <el-button type="primary" @click="startAllUploads" class="upload-btn">
            全部开始上传
          </el-button>
          <el-button @click="clearUploadList"> 清空列表 </el-button>
        </div>
      </div>

      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="previewVideo?.title"
      width="800px"
    >
      <div v-if="previewVideo" class="video-preview">
        <video
          v-if="previewVideo.play_url"
          :src="previewVideo.play_url"
          controls
          width="100%"
          height="400"
        />
        <div v-else class="no-video">
          <el-icon><VideoPlay /></el-icon>
          <p>视频暂不可播放</p>
        </div>

        <div class="video-info">
          <p>
            <strong>描述：</strong>{{ previewVideo.description || "无描述" }}
          </p>
          <p>
            <strong>时长：</strong>{{ formatDuration(previewVideo.duration) }}
          </p>
          <p>
            <strong>分辨率：</strong>{{ previewVideo.width }}x{{
              previewVideo.height
            }}
          </p>
          <p><strong>格式：</strong>{{ previewVideo.format }}</p>
          <p>
            <strong>文件大小：</strong
            >{{ formatFileSize(previewVideo.file_size) }}
          </p>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="editingId ? '编辑视频' : '新增视频'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入视频标题" />
        </el-form-item>
        <el-form-item label="视频描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入视频描述"
          />
        </el-form-item>
        <el-form-item label="分类" prop="category_id">
          <el-select
            v-model="formData.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 100%"
          >
            <el-option label="未分类" :value="0" />
            <el-option
              v-for="category in categoryOptions"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="editingId" label="状态" prop="status">
          <el-select v-model="formData.status" style="width: 100%">
            <el-option label="上传中" :value="1" />
            <el-option label="转码中" :value="2" />
            <el-option label="正常" :value="3" />
            <el-option label="失败" :value="4" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules, UploadInstance } from "element-plus";
import {
  Search,
  Refresh,
  Upload,
  Plus,
  VideoPlay,
} from "@element-plus/icons-vue";
import {
  getVideoList,
  getVideo,
  createVideo,
  updateVideo,
  deleteVideo,
  getUploadSignature,
} from "@/api/video";
import { getCategoryTree } from "@/api/category";
import type {
  Video,
  VideoListRequest,
  VideoCreateRequest,
  VideoUpdateRequest,
  VideoUploadStatus,
} from "@/types/video";
import type { Category } from "@/types/category";

// 腾讯云 VOD SDK
// @ts-ignore
import TcVod from "vod-js-sdk-v6";

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const videoList = ref<Video[]>([]);
const categoryOptions = ref<Category[]>([]);

// 搜索表单
const searchForm = reactive<VideoListRequest>({
  page: 1,
  page_size: 10,
  title: "",
  status: undefined,
  category_id: undefined,
});

interface UploadResult {
  fileId: string;
  video: {
    url: string;
    verify_content: string;
  };
}

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

// 对话框显示状态
const showUploadDialog = ref(false);
const showPreviewDialog = ref(false);
const showEditDialog = ref(false);

// 编辑相关
const editingId = ref<number | null>(null);
const formRef = ref<FormInstance>();
const formData = reactive<VideoCreateRequest & VideoUpdateRequest>({
  title: "",
  description: "",
  file_name: "",
  file_id: "",
  play_url: "",
  cover_url: "",
  duration: 0,
  file_size: 0,
  width: 0,
  height: 0,
  format: "",
  status: 3,
  category_id: 0,
});

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: "请输入视频标题", trigger: "blur" },
    { max: 200, message: "标题长度不能超过200个字符", trigger: "blur" },
  ],
  description: [
    { max: 1000, message: "描述长度不能超过1000个字符", trigger: "blur" },
  ],
};

// 上传相关
const uploadRef = ref<UploadInstance>();
const uploadList = ref<VideoUploadStatus[]>([]);

// 预览相关
const previewVideo = ref<Video | null>(null);

// 获取视频列表
const fetchVideoList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      page_size: pagination.size,
    };

    const response = await getVideoList(params);
    // 后端返回的数据结构是 { data: { data: [...], page: {...} } }
    videoList.value = response.data?.data || [];

    if (response.data?.page) {
      pagination.total = response.data.page.total;
      pagination.current = response.data.page.current;
      pagination.size = response.data.page.size;
    }
  } catch (error) {
    console.error("获取视频列表失败:", error);
    ElMessage.error("获取视频列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取分类选项
const fetchCategoryOptions = async () => {
  try {
    const response = await getCategoryTree();
    categoryOptions.value = response.data || [];
  } catch (error) {
    console.error("获取分类选项失败:", error);
  }
};

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
  return `${minutes.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (!bytes) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString("zh-CN");
};

// 获取状态类型
const getStatusType = (
  status: number
): "success" | "warning" | "primary" | "info" | "danger" => {
  switch (status) {
    case 1:
      return "warning"; // 上传中
    case 2:
      return "info"; // 转码中
    case 3:
      return "success"; // 正常
    case 4:
      return "danger"; // 失败
    default:
      return "primary";
  }
};

// 获取进度状态
const getProgressStatus = (
  status: string
): "" | "success" | "exception" | "warning" => {
  switch (status) {
    case "uploading":
      return "";
    case "success":
      return "success";
    case "error":
      return "exception";
    default:
      return "";
  }
};

// 预览视频
const handlePreview = (video: Video) => {
  previewVideo.value = video;
  showPreviewDialog.value = true;
};

// 处理编辑
const handleEdit = (video: Video) => {
  editingId.value = video.id;
  Object.assign(formData, {
    title: video.title,
    description: video.description,
    play_url: video.play_url,
    cover_url: video.cover_url,
    duration: video.duration,
    file_size: video.file_size,
    width: video.width,
    height: video.height,
    format: video.format,
    status: video.status,
    category_id: video.category_id,
  });
  showEditDialog.value = true;
};

// 处理删除
const handleDelete = async (video: Video) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除视频 "${video.title}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    await deleteVideo(video.id);
    ElMessage.success("删除成功");
    fetchVideoList();
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除视频失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 保存视频
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (editingId.value) {
      // 更新视频
      await updateVideo(editingId.value, formData as VideoUpdateRequest);
      ElMessage.success("更新成功");
    } else {
      // 创建视频
      await createVideo(formData as VideoCreateRequest);
      ElMessage.success("创建成功");
    }

    showEditDialog.value = false;
    fetchVideoList();
  } catch (error) {
    console.error("保存视频失败:", error);
    ElMessage.error("保存失败");
  } finally {
    saving.value = false;
  }
};

// 文件选择处理
const handleFileSelect = (file: any) => {
  const uploadItem: VideoUploadStatus = {
    file: file.raw,
    title: file.raw.name.replace(/\.[^/.]+$/, ""), // 默认使用文件名作为标题
    cover_url: "",
    category_id: 0,
    progress: { loaded: 0, total: file.raw.size, percent: 0 },
    status: "waiting",
  };
  uploadList.value.push(uploadItem);
};

// 封面图片选择处理
const handleCoverSelect = async (file: any, index: number) => {
  try {
    const uploadItem = uploadList.value[index];
    if (!uploadItem) return;

    // 使用COS上传封面图片
    const formData = new FormData();
    formData.append("file", file.raw);

    // 这里应该调用COS上传API，暂时使用占位符
    // const response = await uploadToCOS(formData);
    // uploadItem.cover_url = response.url;

    // 临时使用本地预览
    const reader = new FileReader();
    reader.onload = (e) => {
      uploadItem.cover_url = e.target?.result as string;
    };
    reader.readAsDataURL(file.raw);

    ElMessage.success("封面上传成功");
  } catch (error) {
    console.error("封面上传失败:", error);
    ElMessage.error("封面上传失败");
  }
};

// 开始上传
const startUpload = async (index: number) => {
  const uploadItem = uploadList.value[index];
  if (!uploadItem || uploadItem.status !== "waiting") return;

  try {
    uploadItem.status = "uploading";

    // 获取上传签名
    const signatureResponse = await getUploadSignature();
    const signature = signatureResponse.data.signature;

    // 初始化腾讯云 VOD 上传器
    const tcVod = new TcVod({
      getSignature: () => signature,
    });

    // 开始上传
    const uploader = tcVod.upload({
      mediaFile: uploadItem.file,
    });

    // 监听上传进度
    uploader.on("media_progress", (info: any) => {
      uploadItem.progress = {
        loaded: info.loaded,
        total: info.total,
        percent: Math.round((info.loaded / info.total) * 100),
      };
    });

    uploader
      .done()
      .then(async function (doneResult: UploadResult) {
        console.log("上传完成:", doneResult);
        uploadItem.status = "success";

        // 自动创建视频记录
        try {
          const videoData: VideoCreateRequest = {
            title:
              uploadItem.title || uploadItem.file.name.replace(/\.[^/.]+$/, ""), // 使用用户输入的标题
            description: "",
            file_name: uploadItem.file.name,
            file_id: doneResult.fileId,
            play_url: "", // 上传完成时还没有转码后的播放地址，等待腾讯云通知更新
            cover_url: uploadItem.cover_url || "", // 优先使用用户上传的封面
            duration: 0, // 等待腾讯云通知更新
            file_size: uploadItem.file.size,
            width: 0, // 等待腾讯云通知更新
            height: 0, // 等待腾讯云通知更新
            format: uploadItem.file.name.split(".").pop() || "",
            category_id: uploadItem.category_id || 0, // 使用用户选择的分类
          };

          await createVideo(videoData);
          ElMessage.success(
            `视频 "${uploadItem.file.name}" 上传成功，正在处理中...`
          );
          fetchVideoList();
        } catch (error) {
          console.error("创建视频记录失败:", error);
          ElMessage.warning("视频上传成功，但创建记录失败，请手动添加");
        }
      })
      .catch(function (err) {
        console.error("视频上传失败:", err);
        uploadItem.status = "error";
        ElMessage.error(`视频 "${uploadItem.file.name}" 上传失败`);
      });

    // 监听上传失败
    uploader.on("error", (error: any) => {
      uploadItem.status = "error";
      uploadItem.error = error.message || "上传失败";
      ElMessage.error(
        `视频 "${uploadItem.file.name}" 上传失败: ${uploadItem.error}`
      );
    });
  } catch (error) {
    uploadItem.status = "error";
    uploadItem.error = "获取上传签名失败";
    console.error("上传失败:", error);
    ElMessage.error("上传失败");
  }
};

// 开始所有上传
const startAllUploads = () => {
  uploadList.value.forEach((item, index) => {
    if (item.status === "waiting" && item.title && item.category_id) {
      startUpload(index);
    }
  });

  // 检查是否有未填写完整信息的视频
  const incompleteItems = uploadList.value.filter(
    (item) => item.status === "waiting" && (!item.title || !item.category_id)
  );

  if (incompleteItems.length > 0) {
    ElMessage.warning(
      `有 ${incompleteItems.length} 个视频信息不完整，请填写标题和分类后再上传`
    );
  }
};

// 移除上传项
const removeUploadItem = (index: number) => {
  uploadList.value.splice(index, 1);
};

// 清空上传列表
const clearUploadList = () => {
  uploadList.value = [];
};

// 重置表单
const resetForm = () => {
  editingId.value = null;
  Object.assign(formData, {
    title: "",
    description: "",
    file_name: "",
    file_id: "",
    play_url: "",
    cover_url: "",
    duration: 0,
    file_size: 0,
    width: 0,
    height: 0,
    format: "",
    status: 3,
    category_id: 0,
  });
  formRef.value?.resetFields();
};

// 组件挂载时执行
onMounted(() => {
  fetchVideoList();
  fetchCategoryOptions();
});
</script>

<style scoped>
.video-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-buttons .el-button {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.action-buttons .el-button--success {
  background: linear-gradient(45deg, #1bb394 0%, #a8e6cf 100%);
}

.action-buttons .el-button--success:hover {
  box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
}

.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-section .el-table {
  border-radius: 12px;
}

.table-section .el-table th {
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

.table-section .el-table tr:hover {
  background: #f8f9ff;
}

.video-cover {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.video-cover:hover {
  transform: scale(1.05);
}

.video-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-cover {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24px;
}

.pagination-section {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.upload-section {
  padding: 20px 0;
}

.upload-list {
  margin-top: 20px;
}

.upload-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  background: #fafafa;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.file-name {
  font-weight: 500;
  color: #333;
}

.file-size {
  color: #666;
  font-size: 12px;
}

.upload-progress {
  margin-bottom: 10px;
}

.upload-actions {
  display: flex;
  gap: 10px;
}

.batch-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
}

.video-preview {
  text-align: center;
}

.video-preview video {
  border-radius: 8px;
  margin-bottom: 20px;
}

.no-video {
  height: 400px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  margin-bottom: 20px;
}

.no-video .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.video-info {
  text-align: left;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.video-info p {
  margin: 8px 0;
  color: #555;
}

/* 上传按钮和刷新按钮样式 */
.upload-btn,
.refresh-btn {
  background: #1bb394 !important;
  border-color: #1bb394 !important;
  color: white !important;
}

.upload-btn:hover,
.refresh-btn:hover {
  background: #17a085 !important;
  border-color: #17a085 !important;
}

/* 封面上传样式 */
.cover-upload {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cover-preview {
  width: 120px;
  height: 68px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.cover-preview:hover {
  border-color: #1bb394;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.cover-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #8c939d;
  font-size: 12px;
}

.cover-tips {
  font-size: 12px;
  color: #909399;
}

/* 视频表单样式 */
.video-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }

  .video-container {
    padding: 10px;
  }
}
</style>
