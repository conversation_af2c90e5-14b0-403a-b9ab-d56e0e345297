<template>
  <div class="question-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>题目管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="题目标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入题目标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="题目分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="option in categorySelectOptions"
              :key="option.id"
              :label="option.label"
              :value="option.id"
              :disabled="option.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题目类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="type in QUESTION_TYPES"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select
            v-model="searchForm.difficulty"
            placeholder="请选择难度"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="level in DIFFICULTY_LEVELS"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增题目
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="title" label="题目标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category.name" label="分类" width="120" align="center" />
        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getQuestionTypeTag(row.type)">
              {{ getQuestionTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getDifficultyTag(row.difficulty)">
              {{ getDifficultyLabel(row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分值" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingId ? '编辑题目' : '新增题目'"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="题目标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入题目标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入题目内容"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="题目分类" prop="category_id">
          <el-select
            v-model="formData.category_id"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option
              v-for="option in categorySelectOptions"
              :key="option.id"
              :label="option.label"
              :value="option.id"
              :disabled="option.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题目类型" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="请选择类型"
            style="width: 100%"
            @change="handleTypeChange"
          >
            <el-option
              v-for="type in QUESTION_TYPES"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选项设置" prop="options" v-if="formData.type === 1 || formData.type === 2">
          <div class="options-container">
            <div
              v-for="(option, index) in formData.options"
              :key="index"
              class="option-item"
            >
              <el-input
                v-model="option.label"
                placeholder="选项标识"
                style="width: 80px; margin-right: 10px"
                maxlength="1"
              />
              <el-input
                v-model="option.content"
                placeholder="选项内容"
                style="flex: 1; margin-right: 10px"
                maxlength="200"
              />
              <el-button
                type="danger"
                size="small"
                @click="removeOption(index)"
                :disabled="formData.options.length <= 2"
              >
                删除
              </el-button>
            </div>
            <el-button type="primary" size="small" @click="addOption" :disabled="formData.options.length >= 6">
              添加选项
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="正确答案" prop="correct_answer">
          <el-input
            v-if="formData.type === 3"
            v-model="formData.correct_answer"
            placeholder="请输入正确答案（true/false）"
          />
          <el-checkbox-group
            v-else-if="formData.type === 2"
            v-model="correctAnswerArray"
            @change="handleCorrectAnswerChange"
          >
            <el-checkbox
              v-for="option in formData.options"
              :key="option.label"
              :label="option.label"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          <el-radio-group
            v-else
            v-model="formData.correct_answer"
          >
            <el-radio
              v-for="option in formData.options"
              :key="option.label"
              :label="option.label"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty">
          <el-select
            v-model="formData.difficulty"
            placeholder="请选择难度"
            style="width: 100%"
          >
            <el-option
              v-for="level in DIFFICULTY_LEVELS"
              :key="level.value"
              :label="level.label"
              :value="level.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input-number
            v-model="formData.score"
            :min="1"
            :max="100"
            placeholder="题目分值"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import {
  getQuestionList,
  createQuestion,
  updateQuestion,
  deleteQuestion
} from '@/api/question'
import { getQuestionCategoryTree } from '@/api/questionCategory'
import type { Question, QuestionOption, QUESTION_TYPES, DIFFICULTY_LEVELS } from '@/types/question'
import { flattenCategoryTreeWithPath, type CategoryOption } from '@/utils/category'

// 导入常量
const QUESTION_TYPES = [
  { label: '单选题', value: 1 },
  { label: '多选题', value: 2 },
  { label: '判断题', value: 3 }
]

const DIFFICULTY_LEVELS = [
  { label: '简单', value: 1 },
  { label: '中等', value: 2 },
  { label: '困难', value: 3 }
]

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const editingId = ref<number | null>(null)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  title: '',
  category_id: undefined as number | undefined,
  type: undefined as number | undefined,
  difficulty: undefined as number | undefined,
  status: undefined as number | undefined
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<Question[]>([])
const categorySelectOptions = ref<CategoryOption[]>([])

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  type: 1,
  options: [] as QuestionOption[],
  correct_answer: '',
  difficulty: 1,
  score: 1,
  category_id: 0,
  status: 1
})

// 多选题正确答案数组
const correctAnswerArray = ref<string[]>([])

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' },
    { min: 1, max: 200, message: '标题长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入题目内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '内容长度在 1 到 1000 个字符', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择题目分类', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  correct_answer: [
    { required: true, message: '请设置正确答案', trigger: 'blur' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请设置题目分值', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '分值在 1 到 100 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取题目类型标签
const getQuestionTypeLabel = (type: number) => {
  const typeObj = QUESTION_TYPES.find(t => t.value === type)
  return typeObj?.label || '未知'
}

const getQuestionTypeTag = (type: number) => {
  const tags = { 1: 'primary', 2: 'success', 3: 'warning' }
  return tags[type as keyof typeof tags] || 'info'
}

// 获取难度标签
const getDifficultyLabel = (difficulty: number) => {
  const levelObj = DIFFICULTY_LEVELS.find(l => l.value === difficulty)
  return levelObj?.label || '未知'
}

const getDifficultyTag = (difficulty: number) => {
  const tags = { 1: 'success', 2: 'warning', 3: 'danger' }
  return tags[difficulty as keyof typeof tags] || 'info'
}

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    const response = await getQuestionCategoryTree()
    const categories = response.data || []
    categorySelectOptions.value = flattenCategoryTreeWithPath(categories)
  } catch (error) {
    console.error('获取分类树失败:', error)
  }
}

// 获取题目列表
const fetchQuestionList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm
    }
    const response = await getQuestionList(params)
    tableData.value = response.data?.list || []
    pagination.total = response.data?.total || 0
  } catch (error) {
    console.error('获取题目列表失败:', error)
    ElMessage.error('获取题目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchQuestionList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    category_id: undefined,
    type: undefined,
    difficulty: undefined,
    status: undefined
  })
  pagination.current = 1
  fetchQuestionList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchQuestionList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchQuestionList()
}

// 新增题目
const handleAdd = () => {
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑题目
const handleEdit = (row: Question) => {
  editingId.value = row.id
  Object.assign(formData, {
    title: row.title,
    content: row.content,
    type: row.type,
    options: [...row.options],
    correct_answer: row.correct_answer,
    difficulty: row.difficulty,
    score: row.score,
    category_id: row.category_id,
    status: row.status
  })
  
  // 处理多选题的正确答案
  if (row.type === 2) {
    correctAnswerArray.value = row.correct_answer.split(',')
  }
  
  dialogVisible.value = true
}

// 删除题目
const handleDelete = async (row: Question) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除题目"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteQuestion(row.id)
    ElMessage.success('删除成功')
    fetchQuestionList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除题目失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 题目类型改变
const handleTypeChange = (type: number) => {
  if (type === 1 || type === 2) {
    // 单选题或多选题，初始化选项
    if (formData.options.length === 0) {
      formData.options = [
        { label: 'A', content: '' },
        { label: 'B', content: '' }
      ]
    }
  } else if (type === 3) {
    // 判断题，清空选项
    formData.options = []
    formData.correct_answer = ''
  }
  correctAnswerArray.value = []
}

// 添加选项
const addOption = () => {
  const nextLabel = String.fromCharCode(65 + formData.options.length) // A, B, C, D...
  formData.options.push({ label: nextLabel, content: '' })
}

// 删除选项
const removeOption = (index: number) => {
  formData.options.splice(index, 1)
  // 重新分配标签
  formData.options.forEach((option, i) => {
    option.label = String.fromCharCode(65 + i)
  })
  // 清空正确答案
  formData.correct_answer = ''
  correctAnswerArray.value = []
}

// 多选题正确答案改变
const handleCorrectAnswerChange = (value: string[]) => {
  formData.correct_answer = value.join(',')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingId.value) {
      await updateQuestion(editingId.value, formData)
      ElMessage.success('更新成功')
    } else {
      await createQuestion(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchQuestionList()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetForm()
}

// 重置表单
const resetForm = () => {
  editingId.value = null
  Object.assign(formData, {
    title: '',
    content: '',
    type: 1,
    options: [
      { label: 'A', content: '' },
      { label: 'B', content: '' }
    ],
    correct_answer: '',
    difficulty: 1,
    score: 1,
    category_id: 0,
    status: 1
  })
  correctAnswerArray.value = []
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryTree()
  fetchQuestionList()
})
</script>

<style scoped>
.question-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.options-container {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
