{"level":"info","msg":"日志系统初始化完成","time":"2025-07-06 15:02:54"}
{"body_size":280,"client_ip":"::1","error":"","latency":518700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 15:03:23","timestamp":"2025-07-06 15:03:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建视频成功: input","time":"2025-07-06 15:03:47"}
{"body_size":560,"client_ip":"::1","error":"","latency":108826500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/videos","status_code":200,"time":"2025-07-06 15:03:47","timestamp":"2025-07-06 15:03:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":65660200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:03:47","timestamp":"2025-07-06 15:03:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"ProcedureStateChanged\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":{\"TaskId\":\"1500039798-procedurev2-b61151a793f76fcb26d1f102d2f06660tt0\",\"Status\":\"FINISH\",\"ErrCode\":0,\"Message\":\"SUCCESS\",\"FileId\":\"3560136625156626323\",\"FileName\":\"input\",\"FileUrl\":\"https://1500039798.vod-qcloud.com/cbc63f73vodsh1500039798/f39411fc3560136625156626323/kLFjbLI8QdcA.mp4\",\"MetaData\":{\"AudioDuration\":287.834,\"AudioStreamSet\":[{\"Bitrate\":128589,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"\",\"Loudness\":0,\"SamplingRate\":44100}],\"Bitrate\":643468,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Duration\":287.858,\"Height\":1080,\"Md5\":\"\",\"Rotate\":0,\"Size\":23153456,\"VideoDuration\":287.8,\"VideoStreamSet\":[{\"Bitrate\":507261,\"Codec\":\"h264\",\"CodecTag\":\"\",\"Codecs\":\"\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"Unknown\"},\"Fps\":25,\"Height\":1080,\"Width\":1920}],\"Width\":1920},\"AiAnalysisResultSet\":[],\"AiRecognitionResultSet\":[],\"AiContentReviewResultSet\":[],\"MediaProcessResultSet\":[{\"Type\":\"Transcode\",\"TranscodeTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T07:03:45Z\",\"FinishTime\":\"2025-07-06T07:04:36Z\",\"Input\":{\"Definition\":100030,\"TraceWatermark\":{\"Definition\":0,\"DefinitionForBStream\":0,\"Switch\":\"\"},\"CopyRightWatermark\":{\"Text\":\"\",\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"WatermarkSet\":[],\"HeadTailSet\":[],\"MosaicSet\":[],\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"Output\":{\"Url\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/v.f100030.mp4\",\"Size\":15547240,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Height\":720,\"Width\":1280,\"Bitrate\":432115,\"Md5\":\"5976defc4abff7604bd5384e4c7becda\",\"Duration\":287.835,\"VideoDuration\":287.8,\"AudioDuration\":287.834,\"VideoStreamSet\":[{\"Bitrate\":297409,\"Codec\":\"h264\",\"CodecTag\":\"avc1\",\"Codecs\":\"avc1.64001f\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"SDR\"},\"Fps\":25,\"Height\":720,\"Width\":1280}],\"AudioStreamSet\":[{\"Bitrate\":128026,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"mp4a.40.2\",\"Loudness\":0,\"SamplingRate\":44100}],\"Definition\":100030,\"DigitalWatermarkType\":\"None\",\"CopyRightWatermarkText\":\"\",\"VMAF\":0}},\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":null,\"AdaptiveDynamicStreamingTask\":null},{\"Type\":\"CoverBySnapshot\",\"TranscodeTask\":null,\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T07:03:45Z\",\"FinishTime\":\"2025-07-06T07:03:50Z\",\"Input\":{\"Definition\":10,\"PositionType\":\"Time\",\"PositionValue\":0,\"WatermarkSet\":[]},\"Output\":{\"CoverUrl\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/coverBySnapshot/coverBySnapshot_10_0.jpg\"}},\"AdaptiveDynamicStreamingTask\":null}],\"SessionContext\":\"\",\"SessionId\":\"\",\"TasksPriority\":0,\"TasksNotifyMode\":\"\",\"Operator\":\"\",\"OperationType\":\"\"},\"FileDeleteEvent\":null,\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"处理任务流完成通知: FileId=3560136625156626323, Status=FINISH","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"任务流处理成功: VideoID=2, FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"视频信息更新成功: ID=2, PlayUrl=https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/v.f100030.mp4","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"开始删除原始视频文件: FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"原始视频文件删除请求已发送: FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"body_size":68,"client_ip":"************","error":"","latency":37423400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 15:04:41","timestamp":"2025-07-06 15:04:41","user_agent":"Go-http-client/1.1"}
{"body_size":1615,"client_ip":"::1","error":"","latency":84590100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:05:39","timestamp":"2025-07-06 15:05:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除视频成功: input","time":"2025-07-06 15:06:36"}
{"body_size":33,"client_ip":"::1","error":"","latency":38157200,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/videos/1","status_code":200,"time":"2025-07-06 15:06:36","timestamp":"2025-07-06 15:06:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":30338900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:36","timestamp":"2025-07-06 15:06:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":53932200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:39","timestamp":"2025-07-06 15:06:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":15448700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:40","timestamp":"2025-07-06 15:06:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":17927200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:41","timestamp":"2025-07-06 15:06:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":17388100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=1","status_code":200,"time":"2025-07-06 15:06:51","timestamp":"2025-07-06 15:06:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":4215500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=2","status_code":200,"time":"2025-07-06 15:06:53","timestamp":"2025-07-06 15:06:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":38917300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=3","status_code":200,"time":"2025-07-06 15:06:56","timestamp":"2025-07-06 15:06:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":5689400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:15:45","timestamp":"2025-07-06 15:15:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":1629600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:15:45","timestamp":"2025-07-06 15:15:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"127.0.0.1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 15:15:57","timestamp":"2025-07-06 15:15:57","user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":3167900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:06","timestamp":"2025-07-06 15:17:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":64299300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:17:06","timestamp":"2025-07-06 15:17:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":2099600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:08","timestamp":"2025-07-06 15:17:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":4131700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:11","timestamp":"2025-07-06 15:17:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建分类成功: 测试2级","time":"2025-07-06 15:17:22"}
{"body_size":210,"client_ip":"::1","error":"","latency":27836400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/categories","status_code":200,"time":"2025-07-06 15:17:22","timestamp":"2025-07-06 15:17:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":12345200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:17:22","timestamp":"2025-07-06 15:17:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":71218200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:30","timestamp":"2025-07-06 15:17:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":195693800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:17:30","timestamp":"2025-07-06 15:17:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":6785200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:18:01","timestamp":"2025-07-06 15:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":6876800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:18:01","timestamp":"2025-07-06 15:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":32122100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:18:08","timestamp":"2025-07-06 15:18:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":61108400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:18:08","timestamp":"2025-07-06 15:18:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
