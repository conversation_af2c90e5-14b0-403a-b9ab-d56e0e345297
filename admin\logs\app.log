{"level":"info","msg":"日志系统初始化完成","time":"2025-07-06 15:02:54"}
{"body_size":280,"client_ip":"::1","error":"","latency":518700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 15:03:23","timestamp":"2025-07-06 15:03:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建视频成功: input","time":"2025-07-06 15:03:47"}
{"body_size":560,"client_ip":"::1","error":"","latency":108826500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/videos","status_code":200,"time":"2025-07-06 15:03:47","timestamp":"2025-07-06 15:03:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":65660200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:03:47","timestamp":"2025-07-06 15:03:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"ProcedureStateChanged\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":{\"TaskId\":\"1500039798-procedurev2-b61151a793f76fcb26d1f102d2f06660tt0\",\"Status\":\"FINISH\",\"ErrCode\":0,\"Message\":\"SUCCESS\",\"FileId\":\"3560136625156626323\",\"FileName\":\"input\",\"FileUrl\":\"https://1500039798.vod-qcloud.com/cbc63f73vodsh1500039798/f39411fc3560136625156626323/kLFjbLI8QdcA.mp4\",\"MetaData\":{\"AudioDuration\":287.834,\"AudioStreamSet\":[{\"Bitrate\":128589,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"\",\"Loudness\":0,\"SamplingRate\":44100}],\"Bitrate\":643468,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Duration\":287.858,\"Height\":1080,\"Md5\":\"\",\"Rotate\":0,\"Size\":23153456,\"VideoDuration\":287.8,\"VideoStreamSet\":[{\"Bitrate\":507261,\"Codec\":\"h264\",\"CodecTag\":\"\",\"Codecs\":\"\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"Unknown\"},\"Fps\":25,\"Height\":1080,\"Width\":1920}],\"Width\":1920},\"AiAnalysisResultSet\":[],\"AiRecognitionResultSet\":[],\"AiContentReviewResultSet\":[],\"MediaProcessResultSet\":[{\"Type\":\"Transcode\",\"TranscodeTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T07:03:45Z\",\"FinishTime\":\"2025-07-06T07:04:36Z\",\"Input\":{\"Definition\":100030,\"TraceWatermark\":{\"Definition\":0,\"DefinitionForBStream\":0,\"Switch\":\"\"},\"CopyRightWatermark\":{\"Text\":\"\",\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"WatermarkSet\":[],\"HeadTailSet\":[],\"MosaicSet\":[],\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"Output\":{\"Url\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/v.f100030.mp4\",\"Size\":15547240,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Height\":720,\"Width\":1280,\"Bitrate\":432115,\"Md5\":\"5976defc4abff7604bd5384e4c7becda\",\"Duration\":287.835,\"VideoDuration\":287.8,\"AudioDuration\":287.834,\"VideoStreamSet\":[{\"Bitrate\":297409,\"Codec\":\"h264\",\"CodecTag\":\"avc1\",\"Codecs\":\"avc1.64001f\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"SDR\"},\"Fps\":25,\"Height\":720,\"Width\":1280}],\"AudioStreamSet\":[{\"Bitrate\":128026,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"mp4a.40.2\",\"Loudness\":0,\"SamplingRate\":44100}],\"Definition\":100030,\"DigitalWatermarkType\":\"None\",\"CopyRightWatermarkText\":\"\",\"VMAF\":0}},\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":null,\"AdaptiveDynamicStreamingTask\":null},{\"Type\":\"CoverBySnapshot\",\"TranscodeTask\":null,\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T07:03:45Z\",\"FinishTime\":\"2025-07-06T07:03:50Z\",\"Input\":{\"Definition\":10,\"PositionType\":\"Time\",\"PositionValue\":0,\"WatermarkSet\":[]},\"Output\":{\"CoverUrl\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/coverBySnapshot/coverBySnapshot_10_0.jpg\"}},\"AdaptiveDynamicStreamingTask\":null}],\"SessionContext\":\"\",\"SessionId\":\"\",\"TasksPriority\":0,\"TasksNotifyMode\":\"\",\"Operator\":\"\",\"OperationType\":\"\"},\"FileDeleteEvent\":null,\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"处理任务流完成通知: FileId=3560136625156626323, Status=FINISH","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"任务流处理成功: VideoID=2, FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"视频信息更新成功: ID=2, PlayUrl=https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/f39411fc3560136625156626323/v.f100030.mp4","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"开始删除原始视频文件: FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"level":"info","msg":"原始视频文件删除请求已发送: FileId=3560136625156626323","time":"2025-07-06 15:04:41"}
{"body_size":68,"client_ip":"************","error":"","latency":37423400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 15:04:41","timestamp":"2025-07-06 15:04:41","user_agent":"Go-http-client/1.1"}
{"body_size":1615,"client_ip":"::1","error":"","latency":84590100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:05:39","timestamp":"2025-07-06 15:05:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除视频成功: input","time":"2025-07-06 15:06:36"}
{"body_size":33,"client_ip":"::1","error":"","latency":38157200,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/videos/1","status_code":200,"time":"2025-07-06 15:06:36","timestamp":"2025-07-06 15:06:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":30338900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:36","timestamp":"2025-07-06 15:06:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":53932200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:39","timestamp":"2025-07-06 15:06:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":15448700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:40","timestamp":"2025-07-06 15:06:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":17927200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:06:41","timestamp":"2025-07-06 15:06:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":17388100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=1","status_code":200,"time":"2025-07-06 15:06:51","timestamp":"2025-07-06 15:06:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":4215500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=2","status_code":200,"time":"2025-07-06 15:06:53","timestamp":"2025-07-06 15:06:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":38917300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026status=3","status_code":200,"time":"2025-07-06 15:06:56","timestamp":"2025-07-06 15:06:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":5689400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:15:45","timestamp":"2025-07-06 15:15:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":1629600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:15:45","timestamp":"2025-07-06 15:15:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":-1,"client_ip":"127.0.0.1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/","status_code":404,"time":"2025-07-06 15:15:57","timestamp":"2025-07-06 15:15:57","user_agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":3167900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:06","timestamp":"2025-07-06 15:17:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":257,"client_ip":"::1","error":"","latency":64299300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:17:06","timestamp":"2025-07-06 15:17:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":2099600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:08","timestamp":"2025-07-06 15:17:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":206,"client_ip":"::1","error":"","latency":4131700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:11","timestamp":"2025-07-06 15:17:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建分类成功: 测试2级","time":"2025-07-06 15:17:22"}
{"body_size":210,"client_ip":"::1","error":"","latency":27836400,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/categories","status_code":200,"time":"2025-07-06 15:17:22","timestamp":"2025-07-06 15:17:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":12345200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:17:22","timestamp":"2025-07-06 15:17:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":71218200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:17:30","timestamp":"2025-07-06 15:17:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":195693800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:17:30","timestamp":"2025-07-06 15:17:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":6785200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 15:18:01","timestamp":"2025-07-06 15:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":6876800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:18:01","timestamp":"2025-07-06 15:18:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":32122100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 15:18:08","timestamp":"2025-07-06 15:18:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":61108400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 15:18:08","timestamp":"2025-07-06 15:18:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"日志系统初始化完成","time":"2025-07-06 16:13:39"}
{"body_size":316,"client_ip":"::1","error":"","latency":291881600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-06 16:14:15","timestamp":"2025-07-06 16:14:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":10776000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-06 16:14:17","timestamp":"2025-07-06 16:14:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":24404800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:14:46","timestamp":"2025-07-06 16:14:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":132067800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:14:46","timestamp":"2025-07-06 16:14:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":280,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 16:15:41","timestamp":"2025-07-06 16:15:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建视频成功: input","time":"2025-07-06 16:16:12"}
{"body_size":560,"client_ip":"::1","error":"","latency":18697200,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/videos","status_code":200,"time":"2025-07-06 16:16:12","timestamp":"2025-07-06 16:16:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":19844000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:13","timestamp":"2025-07-06 16:16:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":5296700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:26","timestamp":"2025-07-06 16:16:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":44742900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:26","timestamp":"2025-07-06 16:16:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":3996900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:37","timestamp":"2025-07-06 16:16:37","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":17360000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:38","timestamp":"2025-07-06 16:16:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":13998200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:46","timestamp":"2025-07-06 16:16:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":13624500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:47","timestamp":"2025-07-06 16:16:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":20244800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:54","timestamp":"2025-07-06 16:16:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":13853600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:55","timestamp":"2025-07-06 16:16:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1376,"client_ip":"::1","error":"","latency":35885400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:16:55","timestamp":"2025-07-06 16:16:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"ProcedureStateChanged\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":{\"TaskId\":\"1500039798-procedurev2-3f269cfaf46d549fac0c349d741db547tt0\",\"Status\":\"FINISH\",\"ErrCode\":0,\"Message\":\"SUCCESS\",\"FileId\":\"3560136625201507134\",\"FileName\":\"input\",\"FileUrl\":\"https://1500039798.vod-qcloud.com/6cd2c8acvodcq1500039798/476b55313560136625201507134/qiKIqbkA3pUA.mp4\",\"MetaData\":{\"AudioDuration\":287.834,\"AudioStreamSet\":[{\"Bitrate\":128589,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"\",\"Loudness\":0,\"SamplingRate\":44100}],\"Bitrate\":643468,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Duration\":287.858,\"Height\":1080,\"Md5\":\"\",\"Rotate\":0,\"Size\":23153456,\"VideoDuration\":287.8,\"VideoStreamSet\":[{\"Bitrate\":507261,\"Codec\":\"h264\",\"CodecTag\":\"\",\"Codecs\":\"\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"Unknown\"},\"Fps\":25,\"Height\":1080,\"Width\":1920}],\"Width\":1920},\"AiAnalysisResultSet\":[],\"AiRecognitionResultSet\":[],\"AiContentReviewResultSet\":[],\"MediaProcessResultSet\":[{\"Type\":\"Transcode\",\"TranscodeTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T08:16:12Z\",\"FinishTime\":\"2025-07-06T08:16:59Z\",\"Input\":{\"Definition\":100030,\"TraceWatermark\":{\"Definition\":0,\"DefinitionForBStream\":0,\"Switch\":\"\"},\"CopyRightWatermark\":{\"Text\":\"\",\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"WatermarkSet\":[],\"HeadTailSet\":[],\"MosaicSet\":[],\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"Output\":{\"Url\":\"https://1500039798.vod-qcloud.com/43bcb654vodtranscq1500039798/476b55313560136625201507134/v.f100030.mp4\",\"Size\":15326812,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Height\":720,\"Width\":1280,\"Bitrate\":425988,\"Md5\":\"d4fa1f670670015032923e7681d48d28\",\"Duration\":287.835,\"VideoDuration\":287.8,\"AudioDuration\":287.834,\"VideoStreamSet\":[{\"Bitrate\":291287,\"Codec\":\"h264\",\"CodecTag\":\"avc1\",\"Codecs\":\"avc1.64001f\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"SDR\"},\"Fps\":25,\"Height\":720,\"Width\":1280}],\"AudioStreamSet\":[{\"Bitrate\":128026,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"mp4a.40.2\",\"Loudness\":0,\"SamplingRate\":44100}],\"Definition\":100030,\"DigitalWatermarkType\":\"None\",\"CopyRightWatermarkText\":\"\",\"VMAF\":0}},\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":null,\"AdaptiveDynamicStreamingTask\":null},{\"Type\":\"CoverBySnapshot\",\"TranscodeTask\":null,\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T08:16:12Z\",\"FinishTime\":\"2025-07-06T08:16:16Z\",\"Input\":{\"Definition\":10,\"PositionType\":\"Time\",\"PositionValue\":0,\"WatermarkSet\":[]},\"Output\":{\"CoverUrl\":\"https://1500039798.vod-qcloud.com/43bcb654vodtranscq1500039798/476b55313560136625201507134/coverBySnapshot/coverBySnapshot_10_0.jpg\"}},\"AdaptiveDynamicStreamingTask\":null}],\"SessionContext\":\"\",\"SessionId\":\"\",\"TasksPriority\":0,\"TasksNotifyMode\":\"\",\"Operator\":\"\",\"OperationType\":\"\"},\"FileDeleteEvent\":null,\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 16:17:03"}
{"level":"info","msg":"处理任务流完成通知: FileId=3560136625201507134, Status=FINISH","time":"2025-07-06 16:17:03"}
{"level":"info","msg":"任务流处理成功: VideoID=3, FileId=3560136625201507134","time":"2025-07-06 16:17:03"}
{"level":"info","msg":"视频信息更新成功: ID=3, PlayUrl=https://1500039798.vod-qcloud.com/43bcb654vodtranscq1500039798/476b55313560136625201507134/v.f100030.mp4","time":"2025-07-06 16:17:03"}
{"body_size":40,"client_ip":"::1","error":"","latency":12489700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:17:06","timestamp":"2025-07-06 16:17:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除原始视频文件成功: responseInfo:{\"Response\":{\"RequestId\":\"4ad8d553-23f7-4a3a-ac45-0fffb95211ae\"}}","time":"2025-07-06 16:17:10"}
{"body_size":68,"client_ip":"*************","error":"","latency":7435064500,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 16:17:10","timestamp":"2025-07-06 16:17:10","user_agent":"Go-http-client/1.1"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"FileDeleted\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":null,\"FileDeleteEvent\":{\"FileIdSet\":[\"3560136625201507134\"],\"FileDeleteResultInfo\":[{\"FileId\":\"3560136625201507134\",\"DeleteParts\":[{\"Type\":\"OriginalFiles\",\"Definition\":0,\"Urls\":[]}]}]},\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 16:17:11"}
{"level":"warning","msg":"未知的通知类型: FileDeleted","time":"2025-07-06 16:17:11"}
{"body_size":68,"client_ip":"***********","error":"","latency":25276700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 16:17:11","timestamp":"2025-07-06 16:17:11","user_agent":"Go-http-client/1.1"}
{"level":"info","msg":"创建题库分类成功: 基本常识","time":"2025-07-06 16:17:29"}
{"body_size":212,"client_ip":"::1","error":"","latency":32351600,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/question-categories","status_code":200,"time":"2025-07-06 16:17:29","timestamp":"2025-07-06 16:17:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":5817000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:17:29","timestamp":"2025-07-06 16:17:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":38790300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:17:38","timestamp":"2025-07-06 16:17:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":155710800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:17:41","timestamp":"2025-07-06 16:17:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":154713900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:17:41","timestamp":"2025-07-06 16:17:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2686000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:17:43","timestamp":"2025-07-06 16:17:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":11270100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:17:43","timestamp":"2025-07-06 16:17:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":32315800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:18:55","timestamp":"2025-07-06 16:18:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":20817600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:18:55","timestamp":"2025-07-06 16:18:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":86296200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:18:56","timestamp":"2025-07-06 16:18:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":1615,"client_ip":"::1","error":"","latency":127574600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:18:56","timestamp":"2025-07-06 16:18:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除视频成功: input","time":"2025-07-06 16:19:02"}
{"body_size":33,"client_ip":"::1","error":"","latency":20548900,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/videos/2","status_code":200,"time":"2025-07-06 16:19:02","timestamp":"2025-07-06 16:19:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":856,"client_ip":"::1","error":"","latency":16969400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:19:02","timestamp":"2025-07-06 16:19:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"更新视频成功: input","time":"2025-07-06 16:19:14"}
{"body_size":800,"client_ip":"::1","error":"","latency":16542000,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/videos/3","status_code":200,"time":"2025-07-06 16:19:14","timestamp":"2025-07-06 16:19:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":4217900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:19:14","timestamp":"2025-07-06 16:19:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":4339600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026category_id=2","status_code":200,"time":"2025-07-06 16:19:18","timestamp":"2025-07-06 16:19:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":5415300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=\u0026category_id=3","status_code":200,"time":"2025-07-06 16:19:20","timestamp":"2025-07-06 16:19:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询考核分类列表失败: invalid connection","time":"2025-07-06 16:21:48"}
{"body_size":33,"client_ip":"::1","error":"","latency":87826800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:21:48","timestamp":"2025-07-06 16:21:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":32963500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:21:53","timestamp":"2025-07-06 16:21:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":160048900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:21:54","timestamp":"2025-07-06 16:21:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":175748200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:21:54","timestamp":"2025-07-06 16:21:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":159874800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:21:54","timestamp":"2025-07-06 16:21:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":4798600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:21:55","timestamp":"2025-07-06 16:21:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":193513000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:25:46","timestamp":"2025-07-06 16:25:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询视频总数失败: invalid connection","time":"2025-07-06 16:25:46"}
{"body_size":33,"client_ip":"::1","error":"","latency":344054600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:25:46","timestamp":"2025-07-06 16:25:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询考核分类列表失败: invalid connection","time":"2025-07-06 16:27:49"}
{"body_size":33,"client_ip":"::1","error":"","latency":1265300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:27:49","timestamp":"2025-07-06 16:27:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":10546400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:27:49","timestamp":"2025-07-06 16:27:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":1189700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:27:49","timestamp":"2025-07-06 16:27:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":3444200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:28:31","timestamp":"2025-07-06 16:28:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":4494900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:38","timestamp":"2025-07-06 16:29:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2493600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:40","timestamp":"2025-07-06 16:29:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":16505000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:40","timestamp":"2025-07-06 16:29:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":26761300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:41","timestamp":"2025-07-06 16:29:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":12304300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:41","timestamp":"2025-07-06 16:29:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":10993700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:42","timestamp":"2025-07-06 16:29:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":19524400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:42","timestamp":"2025-07-06 16:29:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":9846800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:42","timestamp":"2025-07-06 16:29:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":4114400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:29:47","timestamp":"2025-07-06 16:29:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":7660000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:29:47","timestamp":"2025-07-06 16:29:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":15765400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:29:47","timestamp":"2025-07-06 16:29:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":29863000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:29:47","timestamp":"2025-07-06 16:29:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":31206200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:47","timestamp":"2025-07-06 16:29:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":4712000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:29:49","timestamp":"2025-07-06 16:29:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":7746400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:51","timestamp":"2025-07-06 16:29:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1580600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:51","timestamp":"2025-07-06 16:29:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":7126300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:51","timestamp":"2025-07-06 16:29:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":9360600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:29:53","timestamp":"2025-07-06 16:29:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":8923300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:53","timestamp":"2025-07-06 16:29:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":1492100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:29:55","timestamp":"2025-07-06 16:29:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":3923100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:29:55","timestamp":"2025-07-06 16:29:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":11163900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:29:56","timestamp":"2025-07-06 16:29:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":27509700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:29:56","timestamp":"2025-07-06 16:29:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":16977400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:29:57","timestamp":"2025-07-06 16:29:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":6412800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:29:57","timestamp":"2025-07-06 16:29:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":17695400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:30:02","timestamp":"2025-07-06 16:30:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":30341800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:30:02","timestamp":"2025-07-06 16:30:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":11551400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:31:46","timestamp":"2025-07-06 16:31:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":4694400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:31:49","timestamp":"2025-07-06 16:31:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":16995700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:31:49","timestamp":"2025-07-06 16:31:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":26178200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:32:17","timestamp":"2025-07-06 16:32:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":53306300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:32:17","timestamp":"2025-07-06 16:32:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":141001400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:32:17","timestamp":"2025-07-06 16:32:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":182530900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:32:17","timestamp":"2025-07-06 16:32:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":60404900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:32:55","timestamp":"2025-07-06 16:32:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":648206300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:32:56","timestamp":"2025-07-06 16:32:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":1175476600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:32:56","timestamp":"2025-07-06 16:32:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":1240486900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:32:57","timestamp":"2025-07-06 16:32:57","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":28979900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:38:20","timestamp":"2025-07-06 16:38:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":334172900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:38:21","timestamp":"2025-07-06 16:38:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":1004309800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:38:21","timestamp":"2025-07-06 16:38:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":1151200900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:38:21","timestamp":"2025-07-06 16:38:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询考核分类列表失败: invalid connection","time":"2025-07-06 16:42:08"}
{"body_size":33,"client_ip":"::1","error":"","latency":50543200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:42:08","timestamp":"2025-07-06 16:42:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":15326000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:42:10","timestamp":"2025-07-06 16:42:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":31100500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:42:10","timestamp":"2025-07-06 16:42:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":59548100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:42:10","timestamp":"2025-07-06 16:42:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":101968700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:42:10","timestamp":"2025-07-06 16:42:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":6365700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:42:12","timestamp":"2025-07-06 16:42:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":15314800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:43:26","timestamp":"2025-07-06 16:43:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":14978000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:43:26","timestamp":"2025-07-06 16:43:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":35859700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:43:28","timestamp":"2025-07-06 16:43:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":100805300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:43:28","timestamp":"2025-07-06 16:43:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":100016400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:43:46","timestamp":"2025-07-06 16:43:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":103547700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:43:46","timestamp":"2025-07-06 16:43:46","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":2092600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:43:47","timestamp":"2025-07-06 16:43:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":17200800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:43:47","timestamp":"2025-07-06 16:43:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":2606200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:44:26","timestamp":"2025-07-06 16:44:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":20656100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:44:26","timestamp":"2025-07-06 16:44:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":6628300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:44:27","timestamp":"2025-07-06 16:44:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":27875800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:44:27","timestamp":"2025-07-06 16:44:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2671200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:44:43","timestamp":"2025-07-06 16:44:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1715029200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:46:22","timestamp":"2025-07-06 16:46:22","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":1521510600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:46:23","timestamp":"2025-07-06 16:46:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1870141700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:46:42","timestamp":"2025-07-06 16:46:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":163708700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:46:44","timestamp":"2025-07-06 16:46:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":574689800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:46:44","timestamp":"2025-07-06 16:46:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":19259400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:46:59","timestamp":"2025-07-06 16:46:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取题目总数失败: invalid connection","time":"2025-07-06 16:46:59"}
{"body_size":33,"client_ip":"::1","error":"","latency":72229900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:46:59","timestamp":"2025-07-06 16:46:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":65124600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:47:02","timestamp":"2025-07-06 16:47:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":32525100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:47:05","timestamp":"2025-07-06 16:47:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":78889600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:47:05","timestamp":"2025-07-06 16:47:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":2594700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:47:06","timestamp":"2025-07-06 16:47:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":4428800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:47:06","timestamp":"2025-07-06 16:47:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":83008900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:47:09","timestamp":"2025-07-06 16:47:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":108941000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:47:09","timestamp":"2025-07-06 16:47:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":19496400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:47:49","timestamp":"2025-07-06 16:47:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":28516900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:47:49","timestamp":"2025-07-06 16:47:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":32242500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:47:49","timestamp":"2025-07-06 16:47:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":44939900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:47:49","timestamp":"2025-07-06 16:47:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":9712900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:47:52","timestamp":"2025-07-06 16:47:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":32119300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:47:53","timestamp":"2025-07-06 16:47:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":33229200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:47:53","timestamp":"2025-07-06 16:47:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":79171700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:47:53","timestamp":"2025-07-06 16:47:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":112973000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:47:53","timestamp":"2025-07-06 16:47:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":2995000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:48:06","timestamp":"2025-07-06 16:48:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":2994700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:48:06","timestamp":"2025-07-06 16:48:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":82722100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:48:07","timestamp":"2025-07-06 16:48:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":84586000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:48:07","timestamp":"2025-07-06 16:48:07","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":3235600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:48:09","timestamp":"2025-07-06 16:48:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":3702900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:48:11","timestamp":"2025-07-06 16:48:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":50725100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:48:11","timestamp":"2025-07-06 16:48:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":3785900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:48:13","timestamp":"2025-07-06 16:48:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":6653400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:48:13","timestamp":"2025-07-06 16:48:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":2378300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:48:15","timestamp":"2025-07-06 16:48:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":4586700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:48:16","timestamp":"2025-07-06 16:48:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":83007100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:48:16","timestamp":"2025-07-06 16:48:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":148147100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:48:16","timestamp":"2025-07-06 16:48:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":177467500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:48:16","timestamp":"2025-07-06 16:48:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":45719700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:49:29","timestamp":"2025-07-06 16:49:29","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":135678700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:49:31","timestamp":"2025-07-06 16:49:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":62353000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:49:33","timestamp":"2025-07-06 16:49:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":51062800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:49:33","timestamp":"2025-07-06 16:49:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2441700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:49:34","timestamp":"2025-07-06 16:49:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取试卷总数失败: invalid connection","time":"2025-07-06 16:49:34"}
{"body_size":33,"client_ip":"::1","error":"","latency":4137700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:49:34","timestamp":"2025-07-06 16:49:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":8044600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:49:39","timestamp":"2025-07-06 16:49:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":59136700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:49:39","timestamp":"2025-07-06 16:49:39","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":12733800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:49:41","timestamp":"2025-07-06 16:49:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":28473600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:49:41","timestamp":"2025-07-06 16:49:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":35787100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:49:47","timestamp":"2025-07-06 16:49:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":85814600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:49:47","timestamp":"2025-07-06 16:49:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":3680300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:49:48","timestamp":"2025-07-06 16:49:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":10401500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:49:48","timestamp":"2025-07-06 16:49:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":316,"client_ip":"::1","error":"","latency":124772200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/admins?page=1\u0026size=10\u0026keyword=","status_code":200,"time":"2025-07-06 16:49:50","timestamp":"2025-07-06 16:49:50","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":4042900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:49:52","timestamp":"2025-07-06 16:49:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":13225700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:49:52","timestamp":"2025-07-06 16:49:52","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":28903600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:50:41","timestamp":"2025-07-06 16:50:41","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":142072900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:50:43","timestamp":"2025-07-06 16:50:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":216750300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:50:43","timestamp":"2025-07-06 16:50:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":11478400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:50:44","timestamp":"2025-07-06 16:50:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":113352500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:00","timestamp":"2025-07-06 16:51:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":89909100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:00","timestamp":"2025-07-06 16:51:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":5978000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:01","timestamp":"2025-07-06 16:51:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":8979300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:01","timestamp":"2025-07-06 16:51:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":3643900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:03","timestamp":"2025-07-06 16:51:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":17799700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:03","timestamp":"2025-07-06 16:51:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1029300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:04","timestamp":"2025-07-06 16:51:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1181200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:51:05","timestamp":"2025-07-06 16:51:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":15711600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:51:06","timestamp":"2025-07-06 16:51:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":53452500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:51:06","timestamp":"2025-07-06 16:51:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":76559800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:06","timestamp":"2025-07-06 16:51:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":83225500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:51:06","timestamp":"2025-07-06 16:51:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":38142000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:08","timestamp":"2025-07-06 16:51:08","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":491329500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:09","timestamp":"2025-07-06 16:51:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":18424300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:09","timestamp":"2025-07-06 16:51:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":319540300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:51:09","timestamp":"2025-07-06 16:51:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":16047000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:09","timestamp":"2025-07-06 16:51:09","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":47398900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:51:12","timestamp":"2025-07-06 16:51:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":69433300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:51:12","timestamp":"2025-07-06 16:51:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2837100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:51:15","timestamp":"2025-07-06 16:51:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询题库分类列表失败: invalid connection","time":"2025-07-06 16:53:28"}
{"body_size":33,"client_ip":"::1","error":"","latency":2807400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:53:28","timestamp":"2025-07-06 16:53:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"查询题库分类列表失败: invalid connection","time":"2025-07-06 16:53:30"}
{"body_size":33,"client_ip":"::1","error":"","latency":67983900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:53:30","timestamp":"2025-07-06 16:53:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":61105200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:53:30","timestamp":"2025-07-06 16:53:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":4795000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:53:31","timestamp":"2025-07-06 16:53:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":36840300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:53:32","timestamp":"2025-07-06 16:53:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":68197400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:53:32","timestamp":"2025-07-06 16:53:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":24444400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:53:43","timestamp":"2025-07-06 16:53:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":22020500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:53:45","timestamp":"2025-07-06 16:53:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":142188400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:53:45","timestamp":"2025-07-06 16:53:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":174383500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:54:20","timestamp":"2025-07-06 16:54:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":95959800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:20","timestamp":"2025-07-06 16:54:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":8158100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:54:21","timestamp":"2025-07-06 16:54:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":19176000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:54:24","timestamp":"2025-07-06 16:54:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":36556500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:54:25","timestamp":"2025-07-06 16:54:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":49576600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:54:25","timestamp":"2025-07-06 16:54:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":85665500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:25","timestamp":"2025-07-06 16:54:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":87765400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:54:25","timestamp":"2025-07-06 16:54:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":32438800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:54:28","timestamp":"2025-07-06 16:54:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":36234800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:54:36","timestamp":"2025-07-06 16:54:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":141944800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:36","timestamp":"2025-07-06 16:54:36","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":9411100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:54:38","timestamp":"2025-07-06 16:54:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":50908800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:38","timestamp":"2025-07-06 16:54:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":72505800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:54:40","timestamp":"2025-07-06 16:54:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":97904500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:54:40","timestamp":"2025-07-06 16:54:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":123136000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:40","timestamp":"2025-07-06 16:54:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":150062500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:54:40","timestamp":"2025-07-06 16:54:40","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":3363700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:54:42","timestamp":"2025-07-06 16:54:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":4031100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:54:42","timestamp":"2025-07-06 16:54:42","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":10014900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:54:44","timestamp":"2025-07-06 16:54:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":32778700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:54:44","timestamp":"2025-07-06 16:54:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":2378400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:54:59","timestamp":"2025-07-06 16:54:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":12121900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:55:02","timestamp":"2025-07-06 16:55:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":24100500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:55:02","timestamp":"2025-07-06 16:55:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":86387300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:55:02","timestamp":"2025-07-06 16:55:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":107147500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:55:02","timestamp":"2025-07-06 16:55:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":15725000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:55:13","timestamp":"2025-07-06 16:55:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":3652100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:55:15","timestamp":"2025-07-06 16:55:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":23321500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:55:15","timestamp":"2025-07-06 16:55:15","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":2255800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:55:44","timestamp":"2025-07-06 16:55:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":15423200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:55:45","timestamp":"2025-07-06 16:55:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":15838900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:55:47","timestamp":"2025-07-06 16:55:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2018600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:55:49","timestamp":"2025-07-06 16:55:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":30668400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:55:49","timestamp":"2025-07-06 16:55:49","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1376800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:55:51","timestamp":"2025-07-06 16:55:51","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":13842800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:00","timestamp":"2025-07-06 16:56:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":33175400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:56:00","timestamp":"2025-07-06 16:56:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":83306000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:56:02","timestamp":"2025-07-06 16:56:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":83814900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:02","timestamp":"2025-07-06 16:56:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":1049200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:06","timestamp":"2025-07-06 16:56:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":38531200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:56:06","timestamp":"2025-07-06 16:56:06","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":19384800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:56:10","timestamp":"2025-07-06 16:56:10","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2033400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:56:13","timestamp":"2025-07-06 16:56:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":31571600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:56:13","timestamp":"2025-07-06 16:56:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":100931500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:18","timestamp":"2025-07-06 16:56:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":119396000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:56:18","timestamp":"2025-07-06 16:56:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":40414700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:20","timestamp":"2025-07-06 16:56:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":60366300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:56:20","timestamp":"2025-07-06 16:56:20","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":5428600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:24","timestamp":"2025-07-06 16:56:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":71680800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:56:24","timestamp":"2025-07-06 16:56:24","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":3031900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:27","timestamp":"2025-07-06 16:56:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":18128500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:56:27","timestamp":"2025-07-06 16:56:27","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1946000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:56:28","timestamp":"2025-07-06 16:56:28","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":13933200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:56:34","timestamp":"2025-07-06 16:56:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":46915600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:56:34","timestamp":"2025-07-06 16:56:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":1230700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:56:44","timestamp":"2025-07-06 16:56:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2777700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:17","timestamp":"2025-07-06 16:57:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":82102500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:57:17","timestamp":"2025-07-06 16:57:17","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":4515400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:19","timestamp":"2025-07-06 16:57:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":61752000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:57:19","timestamp":"2025-07-06 16:57:19","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":10363500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:57:21","timestamp":"2025-07-06 16:57:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":5914200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:57:21","timestamp":"2025-07-06 16:57:21","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":3015600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:57:23","timestamp":"2025-07-06 16:57:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":365940000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:57:23","timestamp":"2025-07-06 16:57:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":3067800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:43","timestamp":"2025-07-06 16:57:43","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":10132800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:45","timestamp":"2025-07-06 16:57:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":47547100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:57:45","timestamp":"2025-07-06 16:57:45","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":122758800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:54","timestamp":"2025-07-06 16:57:54","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":161519800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:57:55","timestamp":"2025-07-06 16:57:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":72054200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:57:56","timestamp":"2025-07-06 16:57:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":41233000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:57:59","timestamp":"2025-07-06 16:57:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":9334100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:58:00","timestamp":"2025-07-06 16:58:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":29311200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:00","timestamp":"2025-07-06 16:58:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":76948500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:00","timestamp":"2025-07-06 16:58:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":82623200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:58:00","timestamp":"2025-07-06 16:58:00","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":4969700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:58:03","timestamp":"2025-07-06 16:58:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":4745000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:03","timestamp":"2025-07-06 16:58:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":3796900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:04","timestamp":"2025-07-06 16:58:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":24079900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:04","timestamp":"2025-07-06 16:58:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":4882800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:12","timestamp":"2025-07-06 16:58:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":45845500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:12","timestamp":"2025-07-06 16:58:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":10142200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:14","timestamp":"2025-07-06 16:58:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":24178800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:14","timestamp":"2025-07-06 16:58:14","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":10604700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:16","timestamp":"2025-07-06 16:58:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":4576000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:58:18","timestamp":"2025-07-06 16:58:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":40214500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:58:30","timestamp":"2025-07-06 16:58:30","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":191050600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:58:31","timestamp":"2025-07-06 16:58:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":265517100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:31","timestamp":"2025-07-06 16:58:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":293260600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:31","timestamp":"2025-07-06 16:58:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":120649700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:33","timestamp":"2025-07-06 16:58:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":427,"client_ip":"::1","error":"","latency":278943900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 16:58:33","timestamp":"2025-07-06 16:58:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":59170400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:58:35","timestamp":"2025-07-06 16:58:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":147098100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:35","timestamp":"2025-07-06 16:58:35","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":10344600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:38","timestamp":"2025-07-06 16:58:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2681500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:44","timestamp":"2025-07-06 16:58:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":29625100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:58:44","timestamp":"2025-07-06 16:58:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":17131000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 16:58:56","timestamp":"2025-07-06 16:58:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":13418000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:59:02","timestamp":"2025-07-06 16:59:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":50573700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:59:02","timestamp":"2025-07-06 16:59:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":120962300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:59:02","timestamp":"2025-07-06 16:59:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":147804500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:59:02","timestamp":"2025-07-06 16:59:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":40,"client_ip":"::1","error":"","latency":1674900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:59:03","timestamp":"2025-07-06 16:59:03","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建考核分类成功: 入门检车","time":"2025-07-06 16:59:16"}
{"body_size":212,"client_ip":"::1","error":"","latency":29735900,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/assessment-categories","status_code":200,"time":"2025-07-06 16:59:16","timestamp":"2025-07-06 16:59:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":2828700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:59:16","timestamp":"2025-07-06 16:59:16","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"更新考核分类成功: 入门检测","time":"2025-07-06 16:59:31"}
{"body_size":224,"client_ip":"::1","error":"","latency":67632400,"level":"info","method":"PUT","msg":"HTTP Request","path":"/api/admin/assessment-categories/1","status_code":200,"time":"2025-07-06 16:59:31","timestamp":"2025-07-06 16:59:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":221,"client_ip":"::1","error":"","latency":2661400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:59:31","timestamp":"2025-07-06 16:59:31","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":221,"client_ip":"::1","error":"","latency":25897600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-categories/tree","status_code":200,"time":"2025-07-06 16:59:32","timestamp":"2025-07-06 16:59:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":33390600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 16:59:32","timestamp":"2025-07-06 16:59:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":92,"client_ip":"::1","error":"","latency":51772200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/exams?page=1\u0026page_size=100\u0026status=1","status_code":200,"time":"2025-07-06 16:59:32","timestamp":"2025-07-06 16:59:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":65629000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/assessment-videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 16:59:32","timestamp":"2025-07-06 16:59:32","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":7038500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=","status_code":200,"time":"2025-07-06 16:59:34","timestamp":"2025-07-06 16:59:34","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":64616500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=\u0026category_id=3","status_code":200,"time":"2025-07-06 16:59:53","timestamp":"2025-07-06 16:59:53","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":1691800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=\u0026category_id=2","status_code":200,"time":"2025-07-06 17:00:01","timestamp":"2025-07-06 17:00:01","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":49768400,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=\u0026category_id=2","status_code":200,"time":"2025-07-06 17:00:02","timestamp":"2025-07-06 17:00:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":11054800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=\u0026category_id=2","status_code":200,"time":"2025-07-06 17:00:04","timestamp":"2025-07-06 17:00:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":101,"client_ip":"::1","error":"","latency":1815000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=100\u0026status=1\u0026title=\u0026category_id=2","status_code":200,"time":"2025-07-06 17:00:05","timestamp":"2025-07-06 17:00:05","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":15213600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 17:00:23","timestamp":"2025-07-06 17:00:23","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":60665300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 17:00:25","timestamp":"2025-07-06 17:00:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":149450900,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:00:25","timestamp":"2025-07-06 17:00:25","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":4450800,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 17:00:26","timestamp":"2025-07-06 17:00:26","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":5050700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 17:03:38","timestamp":"2025-07-06 17:03:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"error","msg":"获取分类总数失败: invalid connection","time":"2025-07-06 17:03:38"}
{"body_size":33,"client_ip":"::1","error":"","latency":15130300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories?page=1\u0026page_size=10","status_code":200,"time":"2025-07-06 17:03:38","timestamp":"2025-07-06 17:03:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":389,"client_ip":"::1","error":"","latency":109847100,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/categories/tree","status_code":200,"time":"2025-07-06 17:03:44","timestamp":"2025-07-06 17:03:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":857,"client_ip":"::1","error":"","latency":174265300,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:03:44","timestamp":"2025-07-06 17:03:44","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"删除视频成功: input","time":"2025-07-06 17:03:47"}
{"body_size":33,"client_ip":"::1","error":"","latency":4261200,"level":"info","method":"DELETE","msg":"HTTP Request","path":"/api/admin/videos/3","status_code":200,"time":"2025-07-06 17:03:47","timestamp":"2025-07-06 17:03:47","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":100,"client_ip":"::1","error":"","latency":235541000,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:03:48","timestamp":"2025-07-06 17:03:48","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":280,"client_ip":"::1","error":"","latency":0,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/auth/get_upload_vedio_signature","status_code":200,"time":"2025-07-06 17:04:02","timestamp":"2025-07-06 17:04:02","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"创建视频成功: 政治","time":"2025-07-06 17:04:12"}
{"body_size":560,"client_ip":"::1","error":"","latency":28036900,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/videos","status_code":200,"time":"2025-07-06 17:04:12","timestamp":"2025-07-06 17:04:12","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":617,"client_ip":"::1","error":"","latency":31419600,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:04:13","timestamp":"2025-07-06 17:04:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":617,"client_ip":"::1","error":"","latency":48567500,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/videos?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:04:33","timestamp":"2025-07-06 17:04:33","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":31359700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 17:04:56","timestamp":"2025-07-06 17:04:56","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":209,"client_ip":"::1","error":"","latency":75573700,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/question-categories/tree","status_code":200,"time":"2025-07-06 17:04:58","timestamp":"2025-07-06 17:04:58","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":91,"client_ip":"::1","error":"","latency":238480200,"level":"info","method":"GET","msg":"HTTP Request","path":"/api/admin/questions?page=1\u0026page_size=10\u0026title=","status_code":200,"time":"2025-07-06 17:04:59","timestamp":"2025-07-06 17:04:59","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"ProcedureStateChanged\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":{\"TaskId\":\"1500039798-procedurev2-17a652d4ba0cddf335e7077e547cff92tt0\",\"Status\":\"FINISH\",\"ErrCode\":0,\"Message\":\"SUCCESS\",\"FileId\":\"3560136625165993024\",\"FileName\":\"政治\",\"FileUrl\":\"https://1500039798.vod-qcloud.com/cbc63f73vodsh1500039798/9a581da13560136625165993024/9aePiYxSceoA.mp4\",\"MetaData\":{\"AudioDuration\":300.002,\"AudioStreamSet\":[{\"Bitrate\":127253,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"\",\"Loudness\":0,\"SamplingRate\":44100}],\"Bitrate\":475459,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Duration\":300.167,\"Height\":1080,\"Md5\":\"\",\"Rotate\":0,\"Size\":17839641,\"VideoDuration\":300.1,\"VideoStreamSet\":[{\"Bitrate\":340575,\"Codec\":\"h264\",\"CodecTag\":\"\",\"Codecs\":\"\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"SDR\"},\"Fps\":30,\"Height\":1080,\"Width\":1920}],\"Width\":1920},\"AiAnalysisResultSet\":[],\"AiRecognitionResultSet\":[],\"AiContentReviewResultSet\":[],\"MediaProcessResultSet\":[{\"Type\":\"Transcode\",\"TranscodeTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T09:04:11Z\",\"FinishTime\":\"2025-07-06T09:05:09Z\",\"Input\":{\"Definition\":100030,\"TraceWatermark\":{\"Definition\":0,\"DefinitionForBStream\":0,\"Switch\":\"\"},\"CopyRightWatermark\":{\"Text\":\"\",\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"WatermarkSet\":[],\"HeadTailSet\":[],\"MosaicSet\":[],\"StartTimeOffset\":0,\"EndTimeOffset\":0},\"Output\":{\"Url\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/9a581da13560136625165993024/v.f100030.mp4\",\"Size\":11055682,\"Container\":\"mov,mp4,m4a,3gp,3g2,mj2\",\"Height\":720,\"Width\":1280,\"Bitrate\":294647,\"Md5\":\"26f690f59612798de9865b05808de7b1\",\"Duration\":300.174,\"VideoDuration\":300.173,\"AudioDuration\":300.002,\"VideoStreamSet\":[{\"Bitrate\":159967,\"Codec\":\"h264\",\"CodecTag\":\"avc1\",\"Codecs\":\"avc1.64001f\",\"DynamicRangeInfo\":{\"HDRType\":\"\",\"Type\":\"SDR\"},\"Fps\":25,\"Height\":720,\"Width\":1280}],\"AudioStreamSet\":[{\"Bitrate\":128019,\"Channel\":2,\"Codec\":\"aac\",\"Codecs\":\"mp4a.40.2\",\"Loudness\":0,\"SamplingRate\":44100}],\"Definition\":100030,\"DigitalWatermarkType\":\"None\",\"CopyRightWatermarkText\":\"\",\"VMAF\":0}},\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":null,\"AdaptiveDynamicStreamingTask\":null},{\"Type\":\"CoverBySnapshot\",\"TranscodeTask\":null,\"AnimatedGraphicTask\":null,\"SnapshotByTimeOffsetTask\":null,\"SampleSnapshotTask\":null,\"ImageSpriteTask\":null,\"CoverBySnapshotTask\":{\"Status\":\"SUCCESS\",\"ErrCode\":0,\"ErrCodeExt\":\"\",\"Message\":\"SUCCESS\",\"Progress\":100,\"BeginProcessTime\":\"2025-07-06T09:04:11Z\",\"FinishTime\":\"2025-07-06T09:04:15Z\",\"Input\":{\"Definition\":10,\"PositionType\":\"Time\",\"PositionValue\":0,\"WatermarkSet\":[]},\"Output\":{\"CoverUrl\":\"https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/9a581da13560136625165993024/coverBySnapshot/coverBySnapshot_10_0.jpg\"}},\"AdaptiveDynamicStreamingTask\":null}],\"SessionContext\":\"\",\"SessionId\":\"\",\"TasksPriority\":0,\"TasksNotifyMode\":\"\",\"Operator\":\"\",\"OperationType\":\"\"},\"FileDeleteEvent\":null,\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 17:05:12"}
{"level":"info","msg":"处理任务流完成通知: FileId=3560136625165993024, Status=FINISH","time":"2025-07-06 17:05:12"}
{"level":"info","msg":"任务流处理成功: VideoID=4, FileId=3560136625165993024","time":"2025-07-06 17:05:12"}
{"level":"info","msg":"视频信息更新成功: ID=4, PlayUrl=https://1500039798.vod-qcloud.com/a2b02d1bvodtranssh1500039798/9a581da13560136625165993024/v.f100030.mp4","time":"2025-07-06 17:05:12"}
{"level":"info","msg":"删除原始视频文件成功: responseInfo:{\"Response\":{\"RequestId\":\"4418b598-aa2d-42e6-9875-d8f73bdc082c\"}}","time":"2025-07-06 17:05:12"}
{"body_size":68,"client_ip":"************","error":"","latency":433342000,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 17:05:12","timestamp":"2025-07-06 17:05:12","user_agent":"Go-http-client/1.1"}
{"level":"info","msg":"收到腾讯云视频通知: {\"EventType\":\"FileDeleted\",\"FileUploadEvent\":null,\"ProcedureStateChangeEvent\":null,\"FileDeleteEvent\":{\"FileIdSet\":[\"3560136625165993024\"],\"FileDeleteResultInfo\":[{\"FileId\":\"3560136625165993024\",\"DeleteParts\":[{\"Type\":\"OriginalFiles\",\"Definition\":0,\"Urls\":[]}]}]},\"PullCompleteEvent\":null,\"EditMediaCompleteEvent\":null,\"ComposeMediaCompleteEvent\":null,\"WechatPublishCompleteEvent\":null,\"TranscodeCompleteEvent\":null,\"ConcatCompleteEvent\":null,\"ClipCompleteEvent\":null,\"CreateImageSpriteCompleteEvent\":null,\"SnapshotByTimeOffsetCompleteEvent\":null,\"WechatMiniProgramPublishEvent\":null,\"WechatMiniProgramPublishCompleteEvent\":null,\"RemoveWatermarkCompleteEvent\":null,\"RestoreMediaCompleteEvent\":null,\"ForbidMediaCompleteEvent\":null,\"SplitMediaCompleteEvent\":null,\"RebuildMediaCompleteEvent\":null,\"FastClipMediaCompleteEvent\":null,\"BackUpMediaCompleteEvent\":null,\"ReviewAudioVideoCompleteEvent\":null,\"DescribeFileAttributesCompleteEvent\":null,\"ExtractTraceWatermarkCompleteEvent\":null,\"ExtractCopyRightWatermarkCompleteEvent\":null,\"QualityInspectCompleteEvent\":null,\"QualityEnhanceCompleteEvent\":null,\"MediaCastStatusChanged\":null,\"PersistenceCompleteEvent\":null,\"ComplexAdaptiveDynamicStreamingCompleteEvent\":null,\"ReduceMediaBitrateCompleteEvent\":null}","time":"2025-07-06 17:05:13"}
{"level":"warning","msg":"未知的通知类型: FileDeleted","time":"2025-07-06 17:05:13"}
{"body_size":68,"client_ip":"***********","error":"","latency":15714700,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/tencent/videonotify","status_code":200,"time":"2025-07-06 17:05:13","timestamp":"2025-07-06 17:05:13","user_agent":"Go-http-client/1.1"}
{"body_size":60,"client_ip":"::1","error":"","latency":7000300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/questions","status_code":200,"time":"2025-07-06 17:06:13","timestamp":"2025-07-06 17:06:13","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":60,"client_ip":"::1","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/questions","status_code":200,"time":"2025-07-06 17:06:18","timestamp":"2025-07-06 17:06:18","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":60,"client_ip":"::1","error":"","latency":767300,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/questions","status_code":200,"time":"2025-07-06 17:06:38","timestamp":"2025-07-06 17:06:38","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body_size":60,"client_ip":"::1","error":"","latency":0,"level":"info","method":"POST","msg":"HTTP Request","path":"/api/admin/questions","status_code":200,"time":"2025-07-06 17:06:58","timestamp":"2025-07-06 17:06:58","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
