<template>
  <div class="assessment-category-container">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="分类名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入分类名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增分类
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="分类名称" min-width="200" />
        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="180"
          align="center"
        >
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingId ? '编辑分类' : '新增分类'"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="父级分类" prop="parent_id">
          <el-select
            v-model="formData.parent_id"
            placeholder="请选择父级分类"
            clearable
            style="width: 100%"
          >
            <el-option label="顶级分类" :value="0" />
            <el-option
              v-for="option in categorySelectOptions"
              :key="option.id"
              :label="option.label"
              :value="option.id"
              :disabled="option.disabled || option.id === editingId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="9999"
            placeholder="数字越小越靠前"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import { Search, Refresh, Plus } from "@element-plus/icons-vue";
import {
  getAssessmentCategoryTree,
  createAssessmentCategory,
  updateAssessmentCategory,
  deleteAssessmentCategory,
} from "@/api/assessmentCategory";
import type { AssessmentCategory } from "@/types/assessmentCategory";
import {
  flattenCategoryTreeWithPath,
  type CategoryOption,
} from "@/utils/category";

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const dialogVisible = ref(false);
const editingId = ref<number | null>(null);
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive({
  name: "",
  status: undefined as number | undefined,
});

// 表格数据
const tableData = ref<AssessmentCategory[]>([]);
const categorySelectOptions = ref<CategoryOption[]>([]);

// 表单数据
const formData = reactive({
  name: "",
  description: "",
  sort: 0,
  status: 1,
  parent_id: 0,
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    {
      min: 1,
      max: 100,
      message: "分类名称长度在 1 到 100 个字符",
      trigger: "blur",
    },
  ],
  description: [
    { max: 500, message: "描述长度不能超过 500 个字符", trigger: "blur" },
  ],
  sort: [
    {
      type: "number",
      min: 0,
      max: 9999,
      message: "排序值在 0 到 9999 之间",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 获取分类树数据
const fetchCategoryTree = async () => {
  try {
    loading.value = true;
    const response = await getAssessmentCategoryTree();
    tableData.value = response.data || [];
    // 转换为带层级显示的选项列表
    categorySelectOptions.value = flattenCategoryTreeWithPath(tableData.value);
  } catch (error) {
    console.error("获取分类树失败:", error);
    ElMessage.error("获取分类树失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  fetchCategoryTree();
};

// 重置搜索
const handleReset = () => {
  searchForm.name = "";
  searchForm.status = undefined;
  fetchCategoryTree();
};

// 新增分类
const handleAdd = () => {
  editingId.value = null;
  resetForm();
  dialogVisible.value = true;
};

// 编辑分类
const handleEdit = (row: AssessmentCategory) => {
  editingId.value = row.id;
  Object.assign(formData, {
    name: row.name,
    description: row.description,
    sort: row.sort,
    status: row.status,
    parent_id: row.parent_id,
  });
  dialogVisible.value = true;
};

// 删除分类
const handleDelete = async (row: AssessmentCategory) => {
  try {
    await ElMessageBox.confirm(`确定要删除分类"${row.name}"吗？`, "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await deleteAssessmentCategory(row.id);
    ElMessage.success("删除成功");
    fetchCategoryTree();
  } catch (error: any) {
    if (error !== "cancel") {
      console.error("删除分类失败:", error);
      ElMessage.error(error.response?.data?.message || "删除失败");
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    if (editingId.value) {
      await updateAssessmentCategory(editingId.value, formData);
      ElMessage.success("更新成功");
    } else {
      await createAssessmentCategory(formData);
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    fetchCategoryTree();
  } catch (error: any) {
    console.error("提交失败:", error);
    ElMessage.error(error.response?.data?.message || "操作失败");
  } finally {
    submitting.value = false;
  }
};

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields();
  resetForm();
};

// 重置表单
const resetForm = () => {
  editingId.value = null;
  Object.assign(formData, {
    name: "",
    description: "",
    sort: 0,
    status: 1,
    parent_id: 0,
  });
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return "";
  return new Date(dateTime).toLocaleString("zh-CN");
};

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryTree();
});
</script>

<style scoped>
.assessment-category-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
