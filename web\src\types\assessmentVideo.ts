import type { AssessmentCategory } from './assessmentCategory'
import type { Exam } from './exam'
import type { Video } from './video'

// 考核视频关联
export interface AssessmentVideoRelation {
  id: number
  assessment_video_id: number
  video_id: number
  sort: number
  video?: Video
}

// 考核视频
export interface AssessmentVideo {
  id: number
  title: string
  description: string
  category_id: number
  exam_id: number
  status: number
  category?: AssessmentCategory
  exam?: Exam
  videos?: AssessmentVideoRelation[]
  created_at: string
  updated_at: string
}

// 考核视频列表请求参数
export interface AssessmentVideoListRequest {
  page?: number
  page_size?: number
  title?: string
  category_id?: number
  status?: number
}

// 创建考核视频请求参数
export interface AssessmentVideoCreateRequest {
  title: string
  description?: string
  category_id?: number
  exam_id?: number
  video_ids: number[]
  status?: number
}

// 更新考核视频请求参数
export interface AssessmentVideoUpdateRequest {
  title: string
  description?: string
  category_id?: number
  exam_id?: number
  video_ids: number[]
  status?: number
}

// 视频选择选项
export interface VideoSelectOption {
  id: number
  title: string
  cover_image: string
  duration: number
  category_name: string
  selected?: boolean
}
