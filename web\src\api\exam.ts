import request from '@/utils/request'
import type { 
  Exam, 
  ExamListRequest, 
  ExamCreateRequest, 
  ExamUpdateRequest,
  ExamQuestionUpdateRequest
} from '@/types/exam'

// 获取试卷列表
export function getExamList(params: ExamListRequest) {
  return request({
    url: '/admin/exams',
    method: 'get',
    params
  })
}

// 获取试卷详情
export function getExam(id: number) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'get'
  })
}

// 创建试卷
export function createExam(data: ExamCreateRequest) {
  return request({
    url: '/admin/exams',
    method: 'post',
    data
  })
}

// 更新试卷
export function updateExam(id: number, data: ExamUpdateRequest) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'put',
    data
  })
}

// 删除试卷
export function deleteExam(id: number) {
  return request({
    url: `/admin/exams/${id}`,
    method: 'delete'
  })
}

// 更新试卷题目
export function updateExamQuestions(id: number, data: ExamQuestionUpdateRequest) {
  return request({
    url: `/admin/exams/${id}/questions`,
    method: 'put',
    data
  })
}
