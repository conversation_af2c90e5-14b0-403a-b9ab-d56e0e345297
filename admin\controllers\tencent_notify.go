package controllers

import (
	"ai_select_admin/config"
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"encoding/json"
	"io"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	vod "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vod/v20180717"
	"gorm.io/gorm"
)

type TencentNotifyController struct{}

func NewTencentNotifyController() *TencentNotifyController {
	return &TencentNotifyController{}
}

// 腾讯云视频上传完成通知结构
type VideoUploadNotify struct {
	EventType       string `json:"EventType"`
	FileUploadEvent struct {
		FileId         string `json:"FileId"`
		MediaBasicInfo struct {
			Name          string   `json:"Name"`
			Description   string   `json:"Description"`
			CreateTime    string   `json:"CreateTime"`
			UpdateTime    string   `json:"UpdateTime"`
			ExpireTime    string   `json:"ExpireTime"`
			ClassId       int      `json:"ClassId"`
			ClassName     string   `json:"ClassName"`
			ClassPath     string   `json:"ClassPath"`
			CoverUrl      string   `json:"CoverUrl"`
			Type          string   `json:"Type"`
			MediaUrl      string   `json:"MediaUrl"`
			TagSet        []string `json:"TagSet"`
			StorageRegion string   `json:"StorageRegion"`
			SourceInfo    struct {
				SourceType    string `json:"SourceType"`
				SourceContext string `json:"SourceContext"`
			} `json:"SourceInfo"`
			Vid string `json:"Vid"`
		} `json:"MediaBasicInfo"`
		ProcedureTaskId        string `json:"ProcedureTaskId"`
		ReviewAudioVideoTaskId string `json:"ReviewAudioVideoTaskId"`
	} `json:"FileUploadEvent"`
}

// 腾讯云任务流处理完成通知结构
type ProcedureTaskNotify struct {
	EventType                 string `json:"EventType"`
	ProcedureStateChangeEvent struct {
		TaskId   string `json:"TaskId"`
		Status   string `json:"Status"`
		ErrCode  int    `json:"ErrCode"`
		Message  string `json:"Message"`
		FileId   string `json:"FileId"`
		FileName string `json:"FileName"`
		FileUrl  string `json:"FileUrl"`
		MetaData struct {
			AudioDuration  float64 `json:"AudioDuration"`
			AudioStreamSet []struct {
				Bitrate      int    `json:"Bitrate"`
				Codec        string `json:"Codec"`
				SamplingRate int    `json:"SamplingRate"`
			} `json:"AudioStreamSet"`
			Bitrate        int     `json:"Bitrate"`
			Container      string  `json:"Container"`
			Duration       float64 `json:"Duration"`
			Height         int     `json:"Height"`
			Rotate         int     `json:"Rotate"`
			Size           int64   `json:"Size"`
			VideoDuration  float64 `json:"VideoDuration"`
			VideoStreamSet []struct {
				Bitrate int    `json:"Bitrate"`
				Codec   string `json:"Codec"`
				Fps     int    `json:"Fps"`
				Height  int    `json:"Height"`
				Width   int    `json:"Width"`
			} `json:"VideoStreamSet"`
			Width int `json:"Width"`
		} `json:"MetaData"`
		MediaProcessResultSet []struct {
			Type          string `json:"Type"`
			TranscodeTask struct {
				Status  string `json:"Status"`
				ErrCode int    `json:"ErrCode"`
				Message string `json:"Message"`
				Input   struct {
					Definition int `json:"Definition"`
				} `json:"Input"`
				Output struct {
					Url            string  `json:"Url"`
					Size           int64   `json:"Size"`
					Container      string  `json:"Container"`
					Height         int     `json:"Height"`
					Width          int     `json:"Width"`
					Bitrate        int     `json:"Bitrate"`
					Md5            string  `json:"Md5"`
					Duration       float64 `json:"Duration"`
					VideoStreamSet []struct {
						Bitrate int    `json:"Bitrate"`
						Codec   string `json:"Codec"`
						Fps     int    `json:"Fps"`
						Height  int    `json:"Height"`
						Width   int    `json:"Width"`
					} `json:"VideoStreamSet"`
					AudioStreamSet []struct {
						Bitrate      int    `json:"Bitrate"`
						Codec        string `json:"Codec"`
						SamplingRate int    `json:"SamplingRate"`
					} `json:"AudioStreamSet"`
					Definition int `json:"Definition"`
				} `json:"Output"`
			} `json:"TranscodeTask"`
			CoverBySnapshotTask struct {
				Status  string `json:"Status"`
				ErrCode int    `json:"ErrCode"`
				Message string `json:"Message"`
				Input   struct {
					Definition    int    `json:"Definition"`
					PositionType  string `json:"PositionType"`
					PositionValue int    `json:"PositionValue"`
				} `json:"Input"`
				Output struct {
					CoverUrl string `json:"CoverUrl"`
				} `json:"Output"`
			} `json:"CoverBySnapshotTask"`
		} `json:"MediaProcessResultSet"`
	} `json:"ProcedureStateChangeEvent"`
}

// VideoNotify 处理腾讯云视频通知
func (tnc *TencentNotifyController) VideoNotify(c *gin.Context) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		logger.Errorf("读取通知请求体失败: %v", err)
		utils.BadRequest(c, "读取请求体失败")
		return
	}

	logger.Infof("收到腾讯云视频通知: %s", string(body))

	// 解析通知类型
	var baseNotify struct {
		EventType string `json:"EventType"`
	}

	if err := json.Unmarshal(body, &baseNotify); err != nil {
		logger.Errorf("解析通知数据失败: %v", err)
		utils.BadRequest(c, "解析通知数据失败")
		return
	}

	switch baseNotify.EventType {
	case "NewFileUpload":
		// 视频上传完成通知
		tnc.handleVideoUploadNotify(body)
	case "ProcedureStateChanged":
		// 任务流处理完成通知
		tnc.handleProcedureTaskNotify(body)
	default:
		logger.Warnf("未知的通知类型: %s", baseNotify.EventType)
	}

	// 返回成功响应
	utils.Success(c, gin.H{"message": "通知处理成功"})
}

// handleVideoUploadNotify 处理视频上传完成通知
func (tnc *TencentNotifyController) handleVideoUploadNotify(body []byte) {
	var notify VideoUploadNotify
	if err := json.Unmarshal(body, &notify); err != nil {
		logger.Errorf("解析视频上传通知失败: %v", err)
		return
	}

	fileId := notify.FileUploadEvent.FileId
	logger.Infof("处理视频上传完成通知: FileId=%s", fileId)

	// 查找对应的视频记录
	var video models.Video
	if err := database.DB.Where("file_id = ?", fileId).First(&video).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warnf("未找到对应的视频记录: FileId=%s", fileId)
		} else {
			logger.Errorf("查询视频记录失败: %v", err)
		}
		return
	}

	// 更新视频状态为转码中
	video.Status = 2 // 转码中
	if err := database.DB.Save(&video).Error; err != nil {
		logger.Errorf("更新视频状态失败: %v", err)
		return
	}

	logger.Infof("视频状态已更新为转码中: ID=%d, FileId=%s", video.ID, fileId)
}

// handleProcedureTaskNotify 处理任务流完成通知
func (tnc *TencentNotifyController) handleProcedureTaskNotify(body []byte) {
	var notify ProcedureTaskNotify
	if err := json.Unmarshal(body, &notify); err != nil {
		logger.Errorf("解析任务流通知失败: %v", err)
		return
	}

	event := notify.ProcedureStateChangeEvent
	logger.Infof("处理任务流完成通知: FileId=%s, Status=%s", event.FileId, event.Status)

	// 查找对应的视频记录
	var video models.Video
	if err := database.DB.Where("file_id = ?", event.FileId).First(&video).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warnf("未找到对应的视频记录: FileId=%s", event.FileId)
		} else {
			logger.Errorf("查询视频记录失败: %v", err)
		}
		return
	}

	if event.Status == "FINISH" && event.ErrCode == 0 {
		// 任务流处理成功
		tnc.handleProcedureSuccess(&video, &notify)
	} else {
		// 任务流处理失败
		tnc.handleProcedureFailure(&video, &notify)
	}
}

// handleProcedureSuccess 处理任务流成功
func (tnc *TencentNotifyController) handleProcedureSuccess(video *models.Video, notify *ProcedureTaskNotify) {
	event := notify.ProcedureStateChangeEvent
	logger.Infof("任务流处理成功: VideoID=%d, FileId=%s", video.ID, event.FileId)

	// 更新视频信息 - 查找转码任务结果
	for _, result := range event.MediaProcessResultSet {
		if result.Type == "Transcode" && result.TranscodeTask.Status == "SUCCESS" {
			output := result.TranscodeTask.Output
			video.PlayUrl = output.Url
			video.Duration = int(output.Duration)
			video.Width = output.Width
			video.Height = output.Height
			video.FileSize = output.Size
			break
		}
	}

	// 更新封面信息 - 查找封面生成任务结果
	for _, result := range event.MediaProcessResultSet {
		if result.Type == "CoverBySnapshot" && result.CoverBySnapshotTask.Status == "SUCCESS" {
			if video.CoverUrl == "" { // 如果用户没有上传自定义封面，使用自动生成的封面
				video.CoverUrl = result.CoverBySnapshotTask.Output.CoverUrl
			}
			break
		}
	}

	// 更新状态为正常
	video.Status = 3

	if err := database.DB.Save(video).Error; err != nil {
		logger.Errorf("更新视频信息失败: %v", err)
		return
	}

	logger.Infof("视频信息更新成功: ID=%d, PlayUrl=%s", video.ID, video.PlayUrl)

	// 删除原始视频文件
	tnc.deleteOriginalVideo(event.FileId)
}

// handleProcedureFailure 处理任务流失败
func (tnc *TencentNotifyController) handleProcedureFailure(video *models.Video, notify *ProcedureTaskNotify) {
	event := notify.ProcedureStateChangeEvent
	logger.Errorf("任务流处理失败: VideoID=%d, FileId=%s, ErrCode=%d, Message=%s",
		video.ID, event.FileId, event.ErrCode, event.Message)

	// 更新状态为失败
	video.Status = 4
	if err := database.DB.Save(video).Error; err != nil {
		logger.Errorf("更新视频状态失败: %v", err)
	}
}

// deleteOriginalVideo 删除原始视频文件
func (tnc *TencentNotifyController) deleteOriginalVideo(fileId string) {
	credential := common.NewCredential(
		config.AppConfig.TencentCloud.SecretId,
		config.AppConfig.TencentCloud.SecretKey,
	)
	// 使用临时密钥示例
	// credential := common.NewTokenCredential("SecretId", "SecretKey", "Token")
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "vod.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := vod.NewClient(credential, "", cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := vod.NewDeleteMediaRequest()

	request.FileId = common.StringPtr(fileId)
	subAppId, _ := strconv.ParseUint(config.AppConfig.TencentCloud.TcVod.SubAppid, 10, 64)
	request.SubAppId = common.Uint64Ptr(subAppId)
	request.DeleteParts = []*vod.MediaDeleteItem{
		&vod.MediaDeleteItem{
			Type: common.StringPtr("OriginalFiles"),
		},
	}
	// 返回的resp是一个DeleteMediaResponse的实例，与请求对象对应
	response, err := client.DeleteMedia(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Errorf("删除原始视频文件失败: %v", err)
		return
	}
	if err != nil {
		panic(err)
	}
	// 输出json格式的字符串回包
	logger.Infof("删除原始视频文件成功: responseInfo:%s", response.ToJsonString())
}
