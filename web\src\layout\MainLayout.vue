<template>
  <div class="layout-container">
    <el-container style="height: 100vh">
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo-section">
          <div class="logo-container">
            <img src="@/assets/logo.png" alt="Logo" class="logo-image" />
            <div v-if="!isCollapse" class="logo-text-container">
              <span class="logo-title">研趣培训管理</span>
              <span class="logo-subtitle">内部培训管理后台</span>
            </div>
          </div>
        </div>

        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/admin">
            <el-icon><UserFilled /></el-icon>
            <template #title>管理员管理</template>
          </el-menu-item>

          <!-- 视频管理 -->
          <el-sub-menu index="video">
            <template #title>
              <el-icon><VideoPlay /></el-icon>
              <span>视频管理</span>
            </template>
            <el-menu-item index="/category">
              <el-icon><FolderOpened /></el-icon>
              <template #title>视频分类</template>
            </el-menu-item>
            <el-menu-item index="/video">
              <el-icon><VideoCamera /></el-icon>
              <template #title>视频列表</template>
            </el-menu-item>
          </el-sub-menu>

          <!-- 题库管理 -->
          <el-sub-menu index="question">
            <template #title>
              <el-icon><EditPen /></el-icon>
              <span>题库管理</span>
            </template>
            <el-menu-item index="/question-category">
              <el-icon><FolderOpened /></el-icon>
              <template #title>题目分类</template>
            </el-menu-item>
            <el-menu-item index="/question">
              <el-icon><Document /></el-icon>
              <template #title>题目管理</template>
            </el-menu-item>
            <el-menu-item index="/exam">
              <el-icon><Notebook /></el-icon>
              <template #title>试卷管理</template>
            </el-menu-item>
          </el-sub-menu>

          <!-- 考核管理 -->
          <el-sub-menu index="assessment">
            <template #title>
              <el-icon><Memo /></el-icon>
              <span>考核管理</span>
            </template>
            <el-menu-item index="/assessment-category">
              <el-icon><FolderOpened /></el-icon>
              <template #title>考核分类</template>
            </el-menu-item>
            <el-menu-item index="/assessment-video">
              <el-icon><VideoCamera /></el-icon>
              <template #title>考核视频</template>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button type="text" @click="toggleCollapse">
              <el-icon size="20">
                <Fold v-if="!isCollapse" />
                <Expand v-else />
              </el-icon>
            </el-button>

            <!-- 面包屑导航 -->
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentBreadcrumb">
                {{ currentBreadcrumb }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar
                  :size="32"
                  :src="getAvatarUrl(userInfo?.avatar || '')"
                  :icon="UserFilled"
                >
                  {{ userInfo?.nickname || userInfo?.username }}
                </el-avatar>
                <span class="username">{{
                  userInfo?.nickname || userInfo?.username
                }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile"
                    >个人信息</el-dropdown-item
                  >
                  <el-dropdown-item command="logout" divided
                    >退出登录</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 内容区 -->
        <el-main class="main-content">
          <RouterView />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter, RouterView } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { useAuthStore } from "@/stores/auth";
import { getAvatarUrl } from "@/utils/image";
import {
  UserFilled,
  FolderOpened,
  VideoPlay,
  Fold,
  Expand,
  ArrowDown,
  EditPen,
  Memo,
  Document,
  Notebook,
  VideoCamera,
} from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const isCollapse = ref(false);

const activeMenu = computed(() => route.path);
const userInfo = computed(() => authStore.userInfo);

// 面包屑导航
const currentBreadcrumb = computed(() => {
  const breadcrumbMap: Record<string, string> = {
    "/admin": "管理员管理",
    "/category": "课程分类管理",
    "/video": "视频管理",
    "/question-category": "题目分类管理",
    "/question": "题目管理",
    "/exam": "试卷管理",
    "/assessment-category": "考核分类管理",
    "/assessment-video": "考核视频管理",
  };
  return breadcrumbMap[route.path] || "";
});

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
};

const handleCommand = async (command: string) => {
  switch (command) {
    case "profile":
      ElMessage.info("个人信息功能待开发");
      break;
    case "logout":
      try {
        await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        authStore.logout();
        router.push("/login");
        ElMessage.success("已退出登录");
      } catch {
        // 用户取消
      }
      break;
  }
};

onMounted(() => {
  authStore.initAuth();
});
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo-section {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  background-color: #2b3a4b;
  padding: 0 16px;
  border-bottom: 1px solid #1f2d3d;
}

.sidebar-menu {
  border-right: none;
  background-color: #304156;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-menu-item.is-active {
  background-color: #263445;
  color: #409eff;
}

/* 子菜单样式 */
.sidebar-menu .el-sub-menu {
  background-color: #304156;
}

.sidebar-menu .el-sub-menu__title {
  color: #bfcbd9;
  background-color: #304156;
}

.sidebar-menu .el-sub-menu__title:hover {
  background-color: #263445;
  color: #409eff;
}

.sidebar-menu .el-sub-menu.is-active .el-sub-menu__title {
  color: #409eff;
}

/* 子菜单项样式 */
.sidebar-menu .el-sub-menu .el-menu-item {
  background-color: #1f2d3d;
  color: #bfcbd9;
  padding-left: 50px;
}

.sidebar-menu .el-sub-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu .el-menu-item.is-active {
  background-color: #263445;
  color: #409eff;
}

/* 子菜单展开时的背景 */
.sidebar-menu .el-sub-menu .el-menu {
  background-color: #1f2d3d;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.logo-image {
  height: 36px;
  width: 36px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.logo-text-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.logo-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logo-subtitle {
  font-size: 12px;
  font-weight: 400;
  color: #b3c0d1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.logo-short {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  letter-spacing: 1px;
}

.breadcrumb {
  margin-left: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 12px;
}

.username {
  margin: 0 8px;
  color: #333;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}
</style>
