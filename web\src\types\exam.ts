import type { Question } from './question'

// 试卷题目关联
export interface ExamQuestion {
  id: number
  exam_id: number
  question_id: number
  score: number
  sort: number
  question?: Question
}

// 试卷
export interface Exam {
  id: number
  title: string
  description: string
  total_score: number
  pass_score: number
  time_limit: number
  status: number
  questions?: ExamQuestion[]
  created_at: string
  updated_at: string
}

// 试卷列表请求参数
export interface ExamListRequest {
  page?: number
  page_size?: number
  title?: string
  status?: number
}

// 创建试卷请求参数
export interface ExamCreateRequest {
  title: string
  description?: string
  total_score?: number
  pass_score?: number
  time_limit?: number
  status?: number
}

// 更新试卷请求参数
export interface ExamUpdateRequest {
  title: string
  description?: string
  total_score?: number
  pass_score?: number
  time_limit?: number
  status?: number
}

// 试卷题目更新请求参数
export interface ExamQuestionUpdateRequest {
  questions: {
    question_id: number
    score: number
    sort: number
  }[]
}

// 题目选择项
export interface QuestionSelectOption {
  id: number
  title: string
  content: string
  type: number
  difficulty: number
  score: number
  category_name: string
  selected?: boolean
  exam_score?: number
}
