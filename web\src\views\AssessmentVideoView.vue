<template>
  <div class="assessment-video-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>考核视频管理</h2>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="考核标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入考核标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="考核分类">
          <el-select
            v-model="searchForm.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="option in assessmentCategorySelectOptions"
              :key="option.id"
              :label="option.label"
              :value="option.id"
              :disabled="option.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
          >
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增考核
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="title" label="考核标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category.name" label="分类" width="120" align="center" />
        <el-table-column prop="exam.title" label="关联试卷" min-width="150" show-overflow-tooltip />
        <el-table-column label="视频数量" width="100" align="center">
          <template #default="{ row }">
            {{ row.videos?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="editingId ? '编辑考核' : '新增考核'"
      width="1000px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="考核标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="请输入考核标题"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核分类" prop="category_id">
              <el-select
                v-model="formData.category_id"
                placeholder="请选择分类"
                style="width: 100%"
              >
                <el-option
                  v-for="option in assessmentCategorySelectOptions"
                  :key="option.id"
                  :label="option.label"
                  :value="option.id"
                  :disabled="option.disabled"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联试卷" prop="exam_id">
              <el-select
                v-model="formData.exam_id"
                placeholder="请选择试卷"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="exam in examOptions"
                  :key="exam.id"
                  :label="exam.title"
                  :value="exam.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="考核描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入考核描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="选择视频" prop="video_ids">
          <div class="video-selection">
            <!-- 视频搜索 -->
            <div class="video-search">
              <el-form :model="videoSearchForm" inline>
                <el-form-item label="视频标题">
                  <el-input
                    v-model="videoSearchForm.title"
                    placeholder="请输入视频标题"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="视频分类">
                  <el-select
                    v-model="videoSearchForm.category_id"
                    placeholder="请选择分类"
                    clearable
                    style="width: 150px"
                  >
                    <el-option
                      v-for="option in videoCategorySelectOptions"
                      :key="option.id"
                      :label="option.label"
                      :value="option.id"
                      :disabled="option.disabled"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleVideoSearch">搜索视频</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 视频列表 -->
            <div class="video-list">
              <el-row :gutter="20">
                <el-col :span="12">
                  <h4>可选视频</h4>
                  <el-table
                    v-loading="videoLoading"
                    :data="availableVideos"
                    style="width: 100%"
                    max-height="300px"
                    @selection-change="handleVideoSelectionChange"
                  >
                    <el-table-column type="selection" width="55" />
                    <el-table-column prop="title" label="视频标题" min-width="150" show-overflow-tooltip />
                    <el-table-column prop="category_name" label="分类" width="100" />
                    <el-table-column label="时长" width="80" align="center">
                      <template #default="{ row }">
                        {{ formatDuration(row.duration) }}
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
                <el-col :span="12">
                  <h4>已选视频 ({{ selectedVideos.length }})</h4>
                  <el-table
                    :data="selectedVideos"
                    style="width: 100%"
                    max-height="300px"
                  >
                    <el-table-column prop="title" label="视频标题" min-width="150" show-overflow-tooltip />
                    <el-table-column prop="category_name" label="分类" width="100" />
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button type="danger" size="small" @click="removeVideo($index)">
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import {
  getAssessmentVideoList,
  createAssessmentVideo,
  updateAssessmentVideo,
  deleteAssessmentVideo,
  getAssessmentVideo
} from '@/api/assessmentVideo'
import { getAssessmentCategoryTree } from '@/api/assessmentCategory'
import { getExamList } from '@/api/exam'
import { getVideoList } from '@/api/video'
import { getCategoryTree } from '@/api/category'
import type { AssessmentVideo, VideoSelectOption } from '@/types/assessmentVideo'
import type { Exam } from '@/types/exam'
import type { Video } from '@/types/video'
import { flattenCategoryTreeWithPath, type CategoryOption } from '@/utils/category'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const videoLoading = ref(false)
const dialogVisible = ref(false)
const editingId = ref<number | null>(null)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  title: '',
  category_id: undefined as number | undefined,
  status: undefined as number | undefined
})

// 视频搜索表单
const videoSearchForm = reactive({
  title: '',
  category_id: undefined as number | undefined
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<AssessmentVideo[]>([])
const assessmentCategorySelectOptions = ref<CategoryOption[]>([])
const videoCategorySelectOptions = ref<CategoryOption[]>([])
const examOptions = ref<Exam[]>([])
const availableVideos = ref<VideoSelectOption[]>([])
const selectedVideos = ref<VideoSelectOption[]>([])

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  category_id: 0,
  exam_id: 0,
  video_ids: [] as number[],
  status: 1
})

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入考核标题', trigger: 'blur' },
    { min: 1, max: 200, message: '标题长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  video_ids: [
    { required: true, message: '请选择至少一个视频', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取考核分类树数据
const fetchAssessmentCategoryTree = async () => {
  try {
    const response = await getAssessmentCategoryTree()
    const categories = response.data || []
    assessmentCategorySelectOptions.value = flattenCategoryTreeWithPath(categories)
  } catch (error) {
    console.error('获取考核分类树失败:', error)
  }
}

// 获取视频分类树数据
const fetchVideoCategoryTree = async () => {
  try {
    const response = await getCategoryTree()
    const categories = response.data || []
    videoCategorySelectOptions.value = flattenCategoryTreeWithPath(categories)
  } catch (error) {
    console.error('获取视频分类树失败:', error)
  }
}

// 获取试卷选项
const fetchExamOptions = async () => {
  try {
    const response = await getExamList({ page: 1, page_size: 100, status: 1 })
    examOptions.value = response.data?.list || []
  } catch (error) {
    console.error('获取试卷列表失败:', error)
  }
}

// 获取考核视频列表
const fetchAssessmentVideoList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      page_size: pagination.size,
      ...searchForm
    }
    const response = await getAssessmentVideoList(params)
    tableData.value = response.data?.list || []
    pagination.total = response.data?.total || 0
  } catch (error) {
    console.error('获取考核视频列表失败:', error)
    ElMessage.error('获取考核视频列表失败')
  } finally {
    loading.value = false
  }
}

// 获取视频列表
const fetchVideoList = async () => {
  try {
    videoLoading.value = true
    const params = {
      page: 1,
      page_size: 100,
      status: 1,
      ...videoSearchForm
    }
    const response = await getVideoList(params)
    const videos = response.data?.list || []
    
    // 转换为选择选项格式
    availableVideos.value = videos.map((v: Video) => ({
      id: v.id,
      title: v.title,
      cover_image: v.cover_image,
      duration: v.duration,
      category_name: v.category?.name || '',
      selected: false
    }))
  } catch (error) {
    console.error('获取视频列表失败:', error)
    ElMessage.error('获取视频列表失败')
  } finally {
    videoLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchAssessmentVideoList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    category_id: undefined,
    status: undefined
  })
  pagination.current = 1
  fetchAssessmentVideoList()
}

// 视频搜索
const handleVideoSearch = () => {
  fetchVideoList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  fetchAssessmentVideoList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  fetchAssessmentVideoList()
}

// 新增考核
const handleAdd = () => {
  editingId.value = null
  resetForm()
  fetchVideoList()
  dialogVisible.value = true
}

// 编辑考核
const handleEdit = async (row: AssessmentVideo) => {
  editingId.value = row.id
  
  try {
    // 获取详细信息
    const response = await getAssessmentVideo(row.id)
    const assessmentVideo = response.data
    
    Object.assign(formData, {
      title: assessmentVideo.title,
      description: assessmentVideo.description,
      category_id: assessmentVideo.category_id,
      exam_id: assessmentVideo.exam_id,
      status: assessmentVideo.status
    })
    
    // 设置已选视频
    selectedVideos.value = (assessmentVideo.videos || []).map((relation: any) => ({
      id: relation.video.id,
      title: relation.video.title,
      cover_image: relation.video.cover_image,
      duration: relation.video.duration,
      category_name: relation.video.category?.name || '',
      selected: true
    }))
    
    formData.video_ids = selectedVideos.value.map(v => v.id)
    
    await fetchVideoList()
    dialogVisible.value = true
  } catch (error) {
    console.error('获取考核详情失败:', error)
    ElMessage.error('获取考核详情失败')
  }
}

// 删除考核
const handleDelete = async (row: AssessmentVideo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除考核"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteAssessmentVideo(row.id)
    ElMessage.success('删除成功')
    fetchAssessmentVideoList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除考核失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 视频选择改变
const handleVideoSelectionChange = (selection: VideoSelectOption[]) => {
  // 添加新选择的视频到已选列表
  selection.forEach(video => {
    if (!selectedVideos.value.find(v => v.id === video.id)) {
      selectedVideos.value.push({
        ...video,
        selected: true
      })
    }
  })
  
  // 更新表单数据
  formData.video_ids = selectedVideos.value.map(v => v.id)
}

// 移除视频
const removeVideo = (index: number) => {
  selectedVideos.value.splice(index, 1)
  formData.video_ids = selectedVideos.value.map(v => v.id)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingId.value) {
      await updateAssessmentVideo(editingId.value, formData)
      ElMessage.success('更新成功')
    } else {
      await createAssessmentVideo(formData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchAssessmentVideoList()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.response?.data?.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetForm()
}

// 重置表单
const resetForm = () => {
  editingId.value = null
  Object.assign(formData, {
    title: '',
    description: '',
    category_id: 0,
    exam_id: 0,
    video_ids: [],
    status: 1
  })
  selectedVideos.value = []
  availableVideos.value = []
  Object.assign(videoSearchForm, {
    title: '',
    category_id: undefined
  })
}

// 格式化时长
const formatDuration = (duration: number) => {
  if (!duration) return '00:00'
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  fetchAssessmentCategoryTree()
  fetchVideoCategoryTree()
  fetchExamOptions()
  fetchAssessmentVideoList()
})
</script>

<style scoped>
.assessment-video-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.video-selection {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
}

.video-search {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.video-list h4 {
  margin: 0 0 15px 0;
  color: #303133;
}
</style>
