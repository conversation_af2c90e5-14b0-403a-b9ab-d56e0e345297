package models

import (
	"time"

	"gorm.io/gorm"
)

// Exam 试卷模型
type Exam struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title       string         `json:"title" gorm:"type:varchar(200);not null;comment:试卷标题"`
	Description string         `json:"description" gorm:"type:text;comment:试卷描述"`
	Duration    int            `json:"duration" gorm:"type:int;default:60;comment:考试时长(分钟)"`
	TotalScore  int            `json:"total_score" gorm:"type:int;default:100;comment:总分"`
	PassScore   int            `json:"pass_score" gorm:"type:int;default:60;comment:及格分"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Questions []ExamQuestion `json:"questions,omitempty" gorm:"foreignKey:ExamID"`
}

// ExamQuestion 试卷题目关联模型
type ExamQuestion struct {
	ID         uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	ExamID     uint           `json:"exam_id" gorm:"type:int;not null;comment:试卷ID"`
	QuestionID uint           `json:"question_id" gorm:"type:int;not null;comment:题目ID"`
	Score      int            `json:"score" gorm:"type:int;default:5;comment:该题分值"`
	Sort       int            `json:"sort" gorm:"type:int;default:0;comment:题目顺序"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Question *Question `json:"question,omitempty" gorm:"foreignKey:QuestionID"`
}

// ExamListRequest 试卷列表请求参数
type ExamListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Title    string `form:"title"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
}

// ExamCreateRequest 创建试卷请求参数
type ExamCreateRequest struct {
	Title       string `json:"title" binding:"required,max=200"`
	Description string `json:"description" binding:"max=1000"`
	Duration    int    `json:"duration" binding:"required,min=1,max=300"`
	TotalScore  int    `json:"total_score" binding:"required,min=1,max=1000"`
	PassScore   int    `json:"pass_score" binding:"required,min=1"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// ExamUpdateRequest 更新试卷请求参数
type ExamUpdateRequest struct {
	Title       string `json:"title" binding:"required,max=200"`
	Description string `json:"description" binding:"max=1000"`
	Duration    int    `json:"duration" binding:"required,min=1,max=300"`
	TotalScore  int    `json:"total_score" binding:"required,min=1,max=1000"`
	PassScore   int    `json:"pass_score" binding:"required,min=1"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
}

// ExamQuestionRequest 试卷题目关联请求参数
type ExamQuestionRequest struct {
	QuestionID uint `json:"question_id" binding:"required,min=1"`
	Score      int  `json:"score" binding:"required,min=1,max=100"`
	Sort       int  `json:"sort" binding:"omitempty,min=0"`
}

// ExamQuestionsUpdateRequest 更新试卷题目请求参数
type ExamQuestionsUpdateRequest struct {
	Questions []ExamQuestionRequest `json:"questions" binding:"required,min=1"`
}

// ExamResponse 试卷响应数据
type ExamResponse struct {
	ID            uint                   `json:"id"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Duration      int                    `json:"duration"`
	TotalScore    int                    `json:"total_score"`
	PassScore     int                    `json:"pass_score"`
	Status        int                    `json:"status"`
	QuestionCount int                    `json:"question_count"`
	Questions     []ExamQuestionResponse `json:"questions,omitempty"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// ExamQuestionResponse 试卷题目关联响应数据
type ExamQuestionResponse struct {
	ID         uint              `json:"id"`
	ExamID     uint              `json:"exam_id"`
	QuestionID uint              `json:"question_id"`
	Score      int               `json:"score"`
	Sort       int               `json:"sort"`
	Question   *QuestionResponse `json:"question,omitempty"`
	CreatedAt  time.Time         `json:"created_at"`
	UpdatedAt  time.Time         `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (e *Exam) ToResponse() *ExamResponse {
	resp := &ExamResponse{
		ID:            e.ID,
		Title:         e.Title,
		Description:   e.Description,
		Duration:      e.Duration,
		TotalScore:    e.TotalScore,
		PassScore:     e.PassScore,
		Status:        e.Status,
		QuestionCount: len(e.Questions),
		CreatedAt:     e.CreatedAt,
		UpdatedAt:     e.UpdatedAt,
	}

	// 转换题目关联数据
	if len(e.Questions) > 0 {
		resp.Questions = make([]ExamQuestionResponse, len(e.Questions))
		for i, eq := range e.Questions {
			resp.Questions[i] = ExamQuestionResponse{
				ID:         eq.ID,
				ExamID:     eq.ExamID,
				QuestionID: eq.QuestionID,
				Score:      eq.Score,
				Sort:       eq.Sort,
				CreatedAt:  eq.CreatedAt,
				UpdatedAt:  eq.UpdatedAt,
			}
			if eq.Question != nil {
				resp.Questions[i].Question = eq.Question.ToResponse()
			}
		}
	}

	return resp
}

// ToResponse 转换为响应格式
func (eq *ExamQuestion) ToResponse() *ExamQuestionResponse {
	resp := &ExamQuestionResponse{
		ID:         eq.ID,
		ExamID:     eq.ExamID,
		QuestionID: eq.QuestionID,
		Score:      eq.Score,
		Sort:       eq.Sort,
		CreatedAt:  eq.CreatedAt,
		UpdatedAt:  eq.UpdatedAt,
	}

	if eq.Question != nil {
		resp.Question = eq.Question.ToResponse()
	}

	return resp
}
